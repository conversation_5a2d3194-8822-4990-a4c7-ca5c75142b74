<template>
    <div>
        <div v-if="restRelatedProductCount" class="outpatient-verify-wrapper" @click="handleGoDiagnosisTreatment">
            <div class="verify-item">
                <div class="verify-item__title">
                    关联项目
                </div>
                <ul class="verify-item__content">
                    <li>
                        <div class="status warn">
                            <abc-icon icon="Attention" size="14px"></abc-icon>提示
                        </div>
                        <div class="abstract warn">
                            输注处方有耗材/费用等关联项目未添加
                        </div>
                    </li>
                </ul>
            </div>
        </div>
        <div v-abc-click-outside="closePopper" class="outpatient-verify-wrapper">
            <div class="verify-item">
                <div class="verify-item__title">
                    <abc-space :size="4" align="start">
                        智能审方
                        <abc-tooltip-info
                            :max-width="420"
                            placement="top-start"
                            style="cursor: pointer;"
                            class="test"
                        >
                            <abc-flex
                                class="outpatient-verify-popover"
                                vertical
                                :gap="8"
                            >
                                <abc-space>
                                    <h4>智能审方</h4>
                                    <abc-text v-if="isDeepseekEnable" theme="gray-light">
                                        内置 DeepSeek 引擎
                                    </abc-text>
                                </abc-space>
                                <p>遵循处方规范和适宜性审核要求，在疾病和药品大数据基础上，使用互联网技术为医生提供的一套智能检查工具。</p>
                                <p>在门诊开方时，系统会检查处方中药品规格、用法用量等格式是否规范，药品配伍禁忌、特殊人群使用禁忌、中药炮制方式、皮试结果判定等情况是否适宜。</p>
                                <p>智能审方可以极大程度上帮助医生避免医疗用药风险，提升医疗服务质量。同时，因为医疗的复杂性，智能审方的结果仅用于对医生的辅助参考，不作为权威结论。</p>
                            </abc-flex>
                        </abc-tooltip-info>
                    </abc-space>
                </div>

                <ul v-if="checkItems && checkItems.length" class="verify-item__content">
                    <template v-for="(item, index) in checkItems">
                        <li
                            v-if="!showMore ? index === 0 : true"
                            :key="index"
                            :class="{
                                done: item.isSigned || item.isSetJingMaDu,
                                'has-border': checkItems.length > 1,
                            }"
                        >
                            <div
                                :class="[
                                    'status',
                                    {
                                        pass: item.level === 'PASS',
                                        warn: item.level === 'WARN',
                                        danger: item.level === 'DANGER',
                                    },
                                ]"
                            >
                                <template v-if="item.level === 'PASS'">
                                    <abc-icon icon="chosen" size="14px"></abc-icon>通过
                                </template>
                                <template v-else-if="item.level === 'DANGER'">
                                    <abc-icon icon="Attention" size="14px"></abc-icon>风险
                                </template>
                                <template v-else>
                                    <abc-icon icon="Attention" size="14px"></abc-icon>提醒
                                </template>
                            </div>

                            <div
                                :class="[
                                    'abstract',
                                    'ellipsis',
                                    {
                                        pass: item.level === 'PASS',
                                        warn: item.level === 'WARN',
                                        danger: item.level === 'DANGER',
                                    },
                                ]"
                            >
                                {{ item.detail.reason || '' }}
                            </div>

                            <div v-if="item.name === 'ControlledNarcoticAndToxicRule'">
                                <abc-button
                                    v-if="item.isSetJingMaDu"
                                    class="cancel-btn"
                                    size="small"
                                    variant="text"
                                    @click="handleSetJingMaDu(item, false)"
                                >
                                    取消设置
                                </abc-button>
                                <abc-dropdown v-else @change="(val) => handleSetJingMaDu(item, true, val)">
                                    <abc-button
                                        slot="reference"
                                        size="small"
                                        variant="text"
                                    >
                                        设置
                                    </abc-button>
                                    <abc-dropdown-item
                                        v-for="it in item.detail.prescriptionType"
                                        :key="`prescription-type-${it}`"
                                        :label="GoodsIngredient2PrescriptionFormPsychotropicNarcoticTypeStr[it]"
                                        :value="it"
                                    >
                                    </abc-dropdown-item>
                                </abc-dropdown>
                            </div>

                            <div v-else :ref="`button${index}`" class="opt-btn-wrapper">
                                <abc-button
                                    v-if="item.isSigned"
                                    class="cancel-btn"
                                    size="small"
                                    variant="text"
                                    :width="92"
                                    @click="handleCancelSign(item)"
                                >
                                    取消签字
                                </abc-button>
                                <template v-else>
                                    <abc-popover
                                        v-if="item.pharmacyType === PharmacyTypeEnum.AIR_PHARMACY"
                                        :key="residentPopover(index)"
                                        placement="left"
                                        :resident-popover="residentPopover(index)"
                                        :popper-options="{
                                            boundariesElement: 'viewport'
                                        }"
                                        theme="yellow"
                                    >
                                        <abc-button
                                            slot="reference"
                                            size="small"
                                            variant="text"
                                            :width="92"
                                            @click="handleSign(item, index)"
                                        >
                                            确认签字
                                        </abc-button>
                                        <div>
                                            空中药房需要完成双签才可调配
                                        </div>
                                    </abc-popover>
                                    <abc-button
                                        v-else
                                        size="small"
                                        variant="text"
                                        :width="92"
                                        @click="handleSign(item, index)"
                                    >
                                        确认签字
                                    </abc-button>
                                </template>
                            </div>
                        </li>
                    </template>

                    <li v-if="checkItems.length > 1">
                        <abc-button
                            v-if="showMore"
                            variant="text"
                            size="small"
                            theme="default"
                            icon="s-up-line-medium"
                            icon-position="right"
                            @click="onClickShowMore"
                        >
                            收起
                        </abc-button>
                        <abc-button
                            v-else
                            variant="text"
                            size="small"
                            theme="default"
                            icon="s-dowline-medium"
                            icon-position="right"
                            @click="onClickShowMore"
                        >
                            展开更多 ({{ checkItems.length - 1 }})
                        </abc-button>
                    </li>
                </ul>
                <ul v-else class="verify-item__content">
                    <li>
                        <div
                            :class="[
                                'status',
                                {
                                    pass: verifyStatus === 'PASS',
                                    warn: verifyStatus === 'WARN',
                                    danger: verifyStatus === 'DANGER',
                                },
                            ]"
                        >
                            <template v-if="verifyStatus === 'PASS'">
                                <abc-icon icon="chosen" size="14px"></abc-icon>通过
                            </template>
                            <template v-else-if="verifyStatus === 'DANGER'">
                                <abc-icon icon="Attention" size="14px"></abc-icon>风险
                            </template>
                            <template v-else>
                                <abc-icon icon="Attention" size="14px"></abc-icon>提醒
                            </template>
                        </div>

                        <div
                            :class="[
                                'abstract',
                                {
                                    pass: verifyStatus === 'PASS',
                                    warn: verifyStatus === 'WARN',
                                    danger: verifyStatus === 'DANGER',
                                },
                            ]"
                        >
                            {{ abstract }}
                        </div>
                    </li>
                </ul>
            </div>

            <div v-if="showPopper" class="outpatient-verify-popper">
                <div class="content-regulation">
                    <div class="left-content">
                        处方规范性
                    </div>
                    <div class="right-content">
                        <div v-for="item in regulation" :key="item.name">
                            <div
                                class="label-status"
                                :class="[
                                    {
                                        pass: item.level === 'PASS',
                                        warn: item.level === 'WARN',
                                        danger: item.level === 'DANGER',
                                    },
                                ]"
                            >
                                <span>
                                    {{ item.cnName }}
                                </span>
                                <span class="status">
                                    <template v-if="item.level === 'PASS'">
                                        <abc-icon icon="chosen" size="14px"></abc-icon>通过
                                    </template>
                                    <template v-else-if="item.level === 'DANGER'">
                                        <abc-icon icon="Attention" size="14px"></abc-icon>风险
                                    </template>
                                    <template v-else> <abc-icon icon="Attention" size="14px"></abc-icon>提醒 </template>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="content-fitness">
                    <div class="left-content">
                        处方适宜性
                    </div>
                    <div class="right-content">
                        <div v-for="item in fitness" :key="item.name">
                            <div class="label-status">
                                <span>{{ item.cnName }}</span>

                                <span
                                    :class="[
                                        'status',
                                        {
                                            pass: item.level === 'PASS',
                                            warn: item.level === 'WARN',
                                            danger: item.level === 'DANGER',
                                        },
                                    ]"
                                >
                                    <template v-if="item.level === 'PASS'">
                                        <abc-icon icon="chosen" size="14px"></abc-icon>通过
                                    </template>
                                    <template v-else-if="item.level === 'DANGER'">
                                        <abc-icon icon="Attention" size="14px"></abc-icon>风险
                                    </template>
                                    <template v-else> <abc-icon icon="Attention" size="14px"></abc-icon>提醒 </template>
                                </span>
                            </div>

                            <!--处方药品的相互作用和配伍禁忌-->
                            <interaction
                                v-if="item.name === 'MedicineInteractionRule'"
                                :interactions="interactions"
                            ></interaction>

                            <template v-else>
                                <div
                                    v-if="item.detail"
                                    :class="[
                                        'verify-detail-wrapper',
                                        {
                                            pass: item.level === 'PASS',
                                            warn: item.level === 'WARN',
                                            danger: item.level === 'DANGER',
                                        },
                                    ]"
                                >
                                    {{ item.detail || '' }}
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script type="text/ecmascript-6">
    import Interaction from './interaction';
    import { PharmacyTypeEnum } from '@abc/constants';
    import localStorage from 'utils/localStorage-handler';
    import { GoodsIngredient2PrescriptionFormPsychotropicNarcoticTypeStr } from 'views/common/inventory/constants';
    import { getUseDeepseekEnableStore } from '@/module-federation-dynamic/deepseek';
    import {
        onBeforeMount, ref,
    } from 'vue';
    import { storeToRefs } from 'pinia';
    import Logger from '@/utils/logger';
    import { SignatureTypeEnum } from '@/common/constants/outpatient';

    const storeKey = '_outpatient_verify_show_more_';

    export default {
        name: 'OutpatientVerify',
        components: {
            Interaction,
        },
        props: {
            verifyOutpatient: {
                validator: (prop) => typeof prop === 'object' || prop === null,
                required: true,
            },
            // 剩余未添加的关联项目数量
            restRelatedProductCount: {
                type: Number,
                default: 0,
            },
            // 门诊提交的postData
            postData: {
                type: Object,
                required: true,
            },
            doctorName: {
                validator: (prop) => typeof prop === 'string' || prop === null,
                required: true,
            },
        },
        setup() {
            const isDeepseekEnable = ref(false);

            const loadDeepseekModules = async () => {
                try {
                    const { useDeepseekEnableStore } = await getUseDeepseekEnableStore();
                    if (!useDeepseekEnableStore) {
                        throw new Error('无法加载 Deepseek 模块');
                    }
                    const store = useDeepseekEnableStore();

                    const { isDeepseekEnable: _isDeepseekEnable } = storeToRefs(store);
                    isDeepseekEnable.value = _isDeepseekEnable;
                } catch (error) {
                    console.error('Failed to load Deepseek modules:', error);
                    Logger.report({
                        scene: 'deepseek_report_scene',
                        data: {
                            info: 'Failed to load Deepseek modules',
                            message: error.message || 'unknown',
                            error,
                        },
                    });
                }
            };

            onBeforeMount(() => {
                loadDeepseekModules();
            });

            return {
                isDeepseekEnable,
            };
        },
        data() {
            return {
                PharmacyTypeEnum,
                GoodsIngredient2PrescriptionFormPsychotropicNarcoticTypeStr,
                showPopper: false,
                expandFit: false,
                interactions: [],
                verifyItems: [],

                signedMedicineMap: new Map(),

                regulation: [],
                fitness: [],
                showMore: localStorage.get(storeKey, true) || 0,
            };
        },
        computed: {

            abstract() {
                let arrDanger = []; let arrWarn = []; let str = ''; let arr = [];
                this.fitness.concat(this.regulation).forEach((item) => {
                    if (item.level === 'DANGER') {
                        if (item.name === 'MedicineInteractionRule') {
                            arrDanger = arrDanger.concat(this.interactions);
                        } else {
                            arrDanger.push({ reason: item.detail });
                        }
                    } else if (item.level === 'WARN') {
                        if (item.name === 'MedicineInteractionRule') {
                            arrWarn = arrWarn.concat(this.interactions);
                        } else {
                            arrWarn.push({ reason: item.detail });
                        }
                    }
                });
                arr = arr.concat(arrDanger).concat(arrWarn);
                if (arr.length) {
                    const it = arr[ 0 ];
                    if (it.influence) {
                        it.influence = it.influence.replace(/{A}/g, `${it.medicineA} `);
                        it.influence = it.influence.replace(/{B}/g, ` ${it.medicineB} `);
                        str = it.influence;
                    } else {
                        str = it.reason;
                    }
                }
                return str || '';
            },

            verifyStatus() {
                const dangerArr = []; const warnArr = [];

                this.regulation.forEach((item) => {
                    if (item.level === 'DANGER') {
                        dangerArr.push(item);
                    } else if (item.level === 'WARN') {
                        warnArr.push(item);
                    }
                });
                this.fitness.forEach((item) => {
                    if (item.level === 'DANGER') {
                        dangerArr.push(item);
                    } else if (item.level === 'WARN') {
                        warnArr.push(item);
                    }
                });
                if (dangerArr.length) {
                    return 'DANGER';
                }
                if (warnArr.length) {
                    return 'WARN';
                }
                return 'PASS';
            },

            checkItems: {
                get() {
                    if (!this.verifyOutpatient) return [];
                    return this.verifyOutpatient.checkItems;
                },
                set(val) {
                    // 外部需要使用 checkItems 中的isSigned数据，这里默认做双向绑定
                    this.verifyOutpatient.checkItems = val;
                },
            },
        },
        watch: {
            verifyOutpatient: {
                handler(val) {
                    this.initVerifyResults(val);
                },
                immediate: true,
            },
        },

        created() {
            this.signedMedicineMap = new Map();
            this.initSignedMedicineMap();
        },

        methods: {
            residentPopover(index) {
                return index === this.checkItems.findIndex((item) => {
                    return item.pharmacyType === PharmacyTypeEnum.AIR_PHARMACY && !item.isSigned;
                });
            },
            initVerifyResults(val) {
                if (!val) return;
                const { verifyItems = {} } = val || {};
                this.checkSignedMedicineMap();
                this.initCheckItemsSigned();

                const {
                    FITNESS,
                    REGULATION,
                } = verifyItems || {};
                this.initRegulation(REGULATION);
                this.initFitness(FITNESS);
            },
            initSignedMedicineMap() {
                this.postData.prescriptionChineseForms.forEach((form) => {
                    this.signedMedicineMap.set(form.keyId, form.verifySignatureStatus ? [ {
                        keyId: form.keyId,
                        status: form.verifySignatureStatus,
                    } ] : []);
                    form.prescriptionFormItems.forEach((item) => {
                        if (item.verifySignatures && item.verifySignatures.length) {
                            this.signedMedicineMap.set(item.keyId, item.verifySignatures);
                        }
                    });
                });
                this.postData.prescriptionWesternForms.forEach((form) => {
                    form.prescriptionFormItems.forEach((item) => {
                        if (item.verifySignatures && item.verifySignatures.length) {
                            this.signedMedicineMap.set(item.keyId, item.verifySignatures);
                        }
                    });
                });
                this.postData.prescriptionInfusionForms.forEach((form) => {
                    form.prescriptionFormItems.forEach((item) => {
                        if (item.verifySignatures && item.verifySignatures.length) {
                            this.signedMedicineMap.set(item.keyId, item.verifySignatures);
                        }
                    });
                });
            },
            /**
             * @desc 对已签的相互作用药品配对关系进行检查，删除不配对的
             * <AUTHOR>
             * @date 2022-06-08 10:48:23
             */
            checkSignedMedicineMap() {
                this.signedMedicineMap.forEach((value, key, map) => {
                    const _arr = value?.filter((it) => this.hasSignedRelative(key, it.keyId, it));
                    if (_arr.length) {
                        map.set(key, _arr);
                    } else {
                        map.delete(key);
                    }
                });
                this.$emit('confirm-signature', this.signedMedicineMap);
            },

            /**
             * @desc 已签的风险提示 是否还存在新的相互左右中，没有需要清除
             * <AUTHOR>
             * @date 2022-06-09 11:01:44
             */
            hasSignedRelative(signedKeyId, valueKeyId, item) {
                if (item.type === SignatureTypeEnum.SHEBAO_RESTRICT) return true;
                return this.checkItems.find((item) => {
                    const {
                        name,
                        detail,
                    } = item;
                    if (name === 'MedicineInteractionRule') {
                        const {
                            keyId1,
                            keyId2,
                        } = detail;
                        return keyId1 === signedKeyId && keyId2 === valueKeyId ||
                            keyId2 === signedKeyId && keyId1 === valueKeyId;
                    }
                    if (name === 'MedicineUsageRule') {
                        const {
                            keyId,
                        } = detail;
                        return signedKeyId === keyId && valueKeyId === keyId;
                    }
                    return false;
                });
            },

            initCheckItemsSigned() {
                this.checkItems.forEach((item) => {
                    const {
                        name,
                        detail,
                    } = item;
                    this.$set(item, 'isSigned', false);
                    if (name === 'MedicineInteractionRule') {
                        const {
                            keyId1,
                            keyId2,
                        } = detail;
                        const signedList1 = this.signedMedicineMap.get(keyId1);
                        const signedList2 = this.signedMedicineMap.get(keyId2);
                        if (signedList1 && signedList2) {
                            item.isSigned = signedList1.find((it) => it.keyId === keyId2) &&
                                signedList2.find((it) => it.keyId === keyId1);
                        }
                    }
                    if (name === 'MedicineUsageRule') {
                        const {
                            keyId,
                        } = detail;
                        const signedList = this.signedMedicineMap.get(keyId);
                        if (signedList) {
                            item.isSigned = !!signedList.find((it) => it.keyId === keyId);
                        }
                    }
                });
            },

            initRegulation(regulation) {
                this.regulation = regulation || [];
            },
            initFitness(fitness) {
                this.fitness = fitness?.filter((item) => item.name !== 'ControlledSubstancesRule' && !item.checkItems?.length) || [];
                this.interactions = [];
                this.fitness = this.fitness.map((item) => {
                    if (item.name === 'MedicineInteractionRule' && item.detail) {
                        try {
                            if (typeof item.detail === 'string') {
                                const detail = JSON.parse(item.detail) || {};
                                detail.chinese = detail.chinese || [];
                                detail.western = detail.western || [];
                                this.interactions = detail.chinese.concat(detail.western);
                            }
                        } catch (e) {
                            console.error(e);
                        }
                    }
                    return item;
                });
            },

            handleSetJingMaDu(item, isSet, val) {
                this.$nextTick(() => {
                    this.$set(item, 'isSetJingMaDu', isSet);
                    this.$emit('confirm-set-jing-ma-du', {
                        isSet, detail: item.detail, val,
                    });
                });
            },
            handleSign(item, index) {
                if (!this.doctorName) {
                    this.$Toast({
                        message: '请选择医生',
                        type: 'error',
                        duration: 2000,
                        referenceEl: this.$refs[`button${index}`][0],
                    });
                    return;
                }
                this.initSignedMedicineMap();
                const {
                    name,
                    detail,
                } = item;
                item.isSigned = true;
                if (name === 'MedicineInteractionRule') {
                    const {
                        keyId1,
                        keyId2,
                        cadn1,
                        cadn2,
                        goodsId1,
                        goodsId2,
                    } = detail;
                    this.updateSignedMedicineMap(keyId1, {
                        keyId: keyId2,
                        goodsId: goodsId2,
                        cadn: cadn2,
                    });
                    this.updateSignedMedicineMap(keyId2, {
                        keyId: keyId1,
                        goodsId: goodsId1,
                        cadn: cadn1,
                    });
                } else if (name === 'MedicineUsageRule') {
                    const {
                        keyId,
                    } = detail;
                    this.updateSignedMedicineMap(keyId, {
                        ...detail,
                    });
                }
                this.$emit('confirm-signature', this.signedMedicineMap);
            },
            handleCancelSign(item) {
                this.initSignedMedicineMap();
                const {
                    name,
                    detail,
                } = item;
                item.isSigned = false;
                if (name === 'MedicineInteractionRule') {
                    const {
                        keyId1,
                        keyId2,
                    } = detail;

                    this.updateSignedMedicineMap(keyId1, {
                        keyId: keyId2,
                    }, true);
                    this.updateSignedMedicineMap(keyId2, {
                        keyId: keyId1,
                    }, true);
                } else if (name === 'MedicineUsageRule') {
                    const {
                        keyId,
                    } = detail;
                    this.updateSignedMedicineMap(keyId, { keyId }, true);
                }
                this.$emit('confirm-signature', this.signedMedicineMap);
            },

            updateSignedMedicineMap(keyId, valueObj, isSplice = false) {
                const result = this.signedMedicineMap.get(keyId);
                if (isSplice) {
                    if (result) {
                        const _arr = result.filter((it) => {
                            return it.type === SignatureTypeEnum.SHEBAO_RESTRICT || it.keyId !== valueObj.keyId;
                        });
                        if (_arr.length) {
                            this.signedMedicineMap.set(keyId, _arr);
                        } else {
                            this.signedMedicineMap.delete(keyId);
                        }
                    }
                    return;
                }
                const _obj = {
                    ...valueObj,
                    status: 1,
                };
                if (result) {
                    result.push(_obj);
                } else {
                    this.signedMedicineMap.set(keyId, [_obj]);
                }
            },

            handleGoDiagnosisTreatment() {
                this.$emit('scroll-to-diagnosis-treatment');
            },
            formatDetail(detail) {
                if (typeof detail === 'string') {
                    detail = JSON.parse(detail);
                }

                if (detail) {
                    detail.western = (typeof detail.western === 'string' ?
                        JSON.parse(detail.western) :
                        detail.western) || [];

                    detail.chinese = (typeof detail.chinese === 'string' ?
                        JSON.parse(detail.chinese) :
                        detail.chinese) || [];

                    return detail.chinese.concat(detail.western);
                }
                return [];

            },

            openPopper() {
                if (this.showPopper) {
                    this.showPopper = false;
                    return false;
                }
                this.showPopper = true;
                // $('.container-wrapper').addClass('hidden-scroll')

                this.$nextTick(() => {
                    const height = $('.outpatient-verify-wrapper').offset().top;
                    $('.outpatient-verify-popper').css({
                        'max-height': `${height - 52}px`,
                    });
                });
            },

            closePopper() {
                this.showPopper = false;
                // $('.container-wrapper.hidden-scroll').removeClass('hidden-scroll');
            },

            onClickShowMore() {
                if (this.showMore) {
                    this.showMore = 0;
                } else {
                    this.showMore = 1;
                }
                localStorage.set(storeKey, this.showMore);
            },
        },
    };
</script>

<style rel="stylesheet/scss" lang="scss">
    @import 'src/styles/theme.scss';

    .outpatient-verify-wrapper {
        position: relative;
        width: 100%;
        background-color: var(--abc-color-div-white);

        & + .outpatient-verify-wrapper {
            border-radius: 0;
        }

        &:not(:last-child) {
            border-bottom: 1px dashed var(--abc-color-P6);
        }

        &:first-child {
            border-top-left-radius: var(--abc-border-radius-small);
            border-top-right-radius: var(--abc-border-radius-small);
        }

        &:nth-child(2) {
            width: 100%;
            margin-top: 0;
            border-top: none;
        }

        .pass {
            color: var(--abc-color-G2);
        }

        .warn {
            color: var(--abc-color-Y2);
        }

        .danger {
            color: #ff3333;

            .iconfont {
                color: #ff3333;
            }
        }

        .verify-item {
            display: flex;
            min-height: 38px;
            padding: 0 12px;
            font-size: 14px;

            .verify-item__title {
                display: flex;
                min-width: 108px;
                line-height: 40px;
                color: var(--abc-color-T2);
            }

            .verify-item__content {
                flex: 1;
                width: 0;

                li {
                    display: flex;
                    align-items: center;
                    padding: 7px 0;
                    line-height: 26px;

                    .abstract {
                        flex: 1;
                        font-size: 14px;
                        line-height: 24px;
                    }

                    .opt-btn-wrapper {
                        position: relative;
                        margin-left: 16px;

                        .cancel-btn {
                            color: var(--abc-color-T2);
                        }
                    }

                    &.done {
                        .status,
                        .abstract,
                        .cis-icon-Attention {
                            color: var(--abc-color-T3);
                            text-decoration: line-through;
                        }
                    }

                    &.has-border {
                        border-bottom: 1px dashed var(--abc-color-P8);
                    }
                }

                .status {
                    align-self: flex-start;
                    min-width: 46px;
                    padding-left: 6px;
                    margin-right: 24px;
                    font-size: 14px;

                    .iconfont {
                        margin-right: 4px;
                        font-size: 14px;
                    }
                }
            }

            > .popper__pr-content {
                margin-left: auto;

                i {
                    width: 20px;
                    font-size: 14px;
                    color: $P3;
                }
            }
        }

        .outpatient-verify-popper {
            position: absolute;
            bottom: 38px;
            left: 180px;
            z-index: 9999;
            width: 565px;
            max-height: 700px;
            padding: 16px;
            overflow-y: auto;
            overflow-y: overlay;
            background: rgba(255, 253, 236, 1);
            border: 1px solid rgba(203, 184, 22, 1);
            box-shadow: 2px 2px 0 0 rgba(0, 0, 0, 0.1);
        }

        .content-regulation {
            border-bottom: 1px solid $P6;
        }

        .content-fitness {
            padding-top: 10px;
        }

        .content-regulation,
        .content-fitness {
            display: flex;
            flex-direction: column;
            font-size: 12px;

            .left-content {
                margin-bottom: 8px;
                font-weight: bold;
                line-height: 16px;
            }

            .label-status {
                display: flex;
                margin-bottom: 8px;
                color: $T2;

                .status {
                    display: flex;
                    align-items: center;
                    margin-left: auto;
                }

                i {
                    margin-right: 4px;
                    font-size: 12px;
                }

                &.pass .status {
                    color: $G2;
                }

                &.warn {
                    color: $Y2;
                }

                &.danger {
                    color: $R2;

                    .iconfont {
                        color: $R2;
                    }
                }
            }

            .verify-detail-wrapper {
                padding: 6px 8px;
                margin-bottom: 10px;
                font-weight: bold;
                border: 1px dashed $P3;
                border-radius: var(--abc-border-radius-small);
            }
        }
    }

    .outpatient-verify-popover {
        padding: 8px 6px;
        font-size: 12px;
        line-height: 16px;
        color: var(--abc-color-T2);
        text-align: justify;

        h4 {
            font-weight: bold;
            color: var(--abc-color-T1);
        }
    }
</style>
