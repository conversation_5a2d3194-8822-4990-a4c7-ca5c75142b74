<template>
    <div
        class="prescription-table-wrapper chinese-table"
        :class="{
            'is-disabled': disabledForm, 'air-pharmacy': form.pharmacyType !== PharmacyTypeEnum.LOCAL_PHARMACY
        }"
        data-cy="中药处方"
    >
        <div class="prescription-header">
            <div class="prescription-title">
                <img class="icon-wrapper" src="~assets/images/icon/traditional.png" alt="" />
                <h5>中药处方{{ translateH }}</h5>

                <abc-select
                    ref="specSelect"
                    v-model="form.specification"
                    :width="60"
                    :disabled="disabledForm"
                    size="tiny"
                    class="unit-select"
                    data-cy="pr-chinese-table-spec-select"
                    @enter="enterEvent"
                    @change="changeSpecification"
                >
                    <abc-option data-cy="option-spec-饮片" label="饮片" value="中药饮片"></abc-option>
                    <abc-option data-cy="option-spec-颗粒" label="颗粒" value="中药颗粒"></abc-option>
                </abc-select>

                <abc-select
                    v-if="showEqConversionRule"
                    v-model="form.eqConversionRule"
                    :width="135"
                    :disabled="disabledForm"
                    size="tiny"
                    class="unit-select"
                    data-cy="pr-chinese-table-eqConversionRule-select"
                    @enter="enterEvent"
                    @change="changeEqConversionRule"
                >
                    <abc-option
                        data-cy="option-eqConversionRule-饮片"
                        label="按等效饮片量开方"
                        :value="EqConversionRuleEnum.EQ_PIECES"
                    ></abc-option>
                    <abc-option
                        data-cy="option-eqConversionRule-颗粒"
                        label="按实际颗粒量开方"
                        :value="EqConversionRuleEnum.REAL_GRANULE"
                    ></abc-option>
                </abc-select>

                <vendors-popover
                    v-if="showChangePharmacyType"
                    ref="vendor-popover"
                    :disabled="disabledForm"
                    :department-id="departmentId"
                    :vendor-id.sync="form.vendorId"
                    :vendor-usage-scope-id.sync="form.vendorUsageScopeId"
                    :specification="form.specification"
                    :pharmacy-type.sync="form.pharmacyType"
                    :pharmacy-no.sync="form.pharmacyNo"
                    :pharmacy-name.sync="form.pharmacyName"
                    :ingredient-price.sync="form.ingredientPrice"
                    :usage-scope-id.sync="form.usageScopeId"
                    :vendor-name.sync="form.vendorName"
                    :dose-count="form.doseCount"
                    :medicine-state-scope-id.sync="form.medicineStateScopeId"
                    :form-items="form.prescriptionFormItems"
                    :top-slogan="airPharmacyTopSlogan"
                    :activate-advertising="airPharmacyActivateAdvertising"
                    :is-show-cooperation-pharmacy="!isConsultation"
                    data-cy="pr-chinese-table-vendor"
                    @changeSpecification="changeSpecification"
                    @changePharmacyType="changePharmacyType"
                    @user-change="handleUserChangeVendor"
                    @changeVendor="changeVendor"
                    @change2Local="change2Local"
                    @changeUsage="selectChineseMedicineUsage"
                    @changeMedicineState="changeMedicineState"
                    @changePharmacyLoading="handleChangePharmacyLoading"
                ></vendors-popover>
            </div>

            <abc-text
                v-if="totalWeightInfo.desc"
                size="mini"
                theme="gray"
                tag="div"
                class="total-weight-info"
            >
                {{ totalWeightInfo.desc }}
                <abc-tooltip-info
                    v-if="airPharmacyTotalTips"
                    :content="airPharmacyTotalTips"
                    :icon-size="12"
                    placement="top"
                >
                </abc-tooltip-info>
                <abc-tooltip-info
                    v-else-if="otherPharmacyTotalTips"
                    :content="otherPharmacyTotalTips"
                    :icon-size="12"
                    placement="top"
                >
                </abc-tooltip-info>
            </abc-text>
            <abc-button
                v-else-if="airPharmacyPromotion.type === CmsResourceType.AIR_PHARMACY_PROMOTION"
                variant="text"
                theme="danger"
                size="small"
                style="font-size: var(--abc-font-size-mini);"
                @click="handleAdvertisement"
            >
                {{ airPharmacyPromotion.title }}
            </abc-button>

            <div class="operation">
                <abc-checkbox-button
                    v-if="showDecoction && isOpenSource && form.pharmacyType === PharmacyTypeEnum.LOCAL_PHARMACY"
                    :value="form.isDecoction"
                    control
                    size="mini"
                    :disabled="disabledForm"
                    data-cy="pr-chinese-table-process-btn"
                    @click="clickDecoction"
                >
                    加工
                </abc-checkbox-button>
                <jing-ma-dropdown
                    v-model="form.psychotropicNarcoticType"
                    :is-need-jing-ma-du="false"
                    :disabled="disabledForm"
                    @change="outpatientFeeChange"
                ></jing-ma-dropdown>
                <abc-button
                    v-if="!disabledForm && isOpenSource && form.pharmacyType === PharmacyTypeEnum.LOCAL_PHARMACY"
                    data-cy="pr-infusion-table-operation-search"
                    variant="text"
                    theme="default"
                    size="small"
                    icon="s-list-search-line"
                    @click="showGoodsSelectDialog = true"
                >
                </abc-button>
                <abc-button
                    v-if="isOpenSource"
                    variant="text"
                    theme="default"
                    size="small"
                    icon="save"
                    data-cy="pr-chinese-table-save-btn"
                    style="margin-left: 0;"
                    @click="saveCommon"
                >
                </abc-button>
                <trash-button-v2
                    v-if="!disabledForm"
                    :is-confirm="deleteNeedConfirm"
                    data-cy="pr-chinese-table-trash-btn"
                    @delete="deletePres"
                >
                </trash-button-v2>
            </div>
        </div>

        <div class="prescription-table">
            <draggable
                v-model="medicineList"
                :disabled="disabledForm"
                ghost-class="abc-sortable-ghost"
                :delay="100"
                :delay-on-touch-only="true"
                @start="startDrag"
                @end="dragging = false"
            >
                <transition-group
                    name="list-complete"
                    :css="false"
                    class="table-body"
                    tag="div"
                    mode="out-in"
                >
                    <div
                        v-for="(medicine, index) in medicineList"
                        ref="tableTd"
                        :key="medicine.id || medicine.keyId || index"
                        class="table-td"
                        :class="{
                            'is-last-row': isLastRow(index),
                            'is-last-col': (index + 1) % 4 === 0,
                        }"
                        :data-cy="`item-${index}`"
                    >
                        <div class="name-wrapper">
                            <template v-if="currentIndex === index || !medicine.name">
                                <!-- 空中药房处方卡片 -->
                                <air-pharmacy-medicine-auto-complete
                                    v-if="isOpenSource && form.pharmacyType === PharmacyTypeEnum.AIR_PHARMACY"
                                    ref="chinese-autocomplete"
                                    :specification="form.specification"
                                    :usage-scope-id="form.usageScopeId"
                                    :vendor-usage-scope-id="form.vendorUsageScopeId"
                                    input-placeholder="药名"
                                    :default-keyword="medicine.name"
                                    :show-detail-price="showDetailPrice"
                                    :inner-width="903"
                                    no-icon
                                    :vendor-id="form.vendorId || ''"
                                    data-cy="pr-chinese-medicine-autocomplete"
                                    @left="handleNameLeft(index)"
                                    @right="handleNameRight(index)"
                                    @up="handleNameUp(index)"
                                    @down="handleNameDown(index)"
                                    @focus="currentIndex = index"
                                    @closePanel="handleClosePanel(medicine, index)"
                                    @select="val => changeMedicine(val, index)"
                                >
                                </air-pharmacy-medicine-auto-complete>
                                <!-- 本地药房处方卡片 -->
                                <medicine-auto-complete
                                    v-else
                                    ref="chinese-autocomplete"
                                    :show-detail-price="showDetailPrice"
                                    :specification="form.specification"
                                    :pharmacy-type="form.pharmacyType"
                                    :pharmacy-no="form.pharmacyNo"
                                    input-placeholder="药名"
                                    :default-keyword="medicine.name"
                                    :cm-spec="medicine.productInfo?.cMSpec ?? ''"
                                    :inner-width="903"
                                    :treat-online-clinic-id="treatOnlineClinicId"
                                    :prescription-form-items="medicineList"
                                    :patient-info="patientInfo"
                                    :medical-record="medicalRecord"
                                    :need-check-stock="needCheckStock"
                                    :shebao-card-info="shebaoCardInfo"
                                    :is-open-source="isOpenSource"
                                    no-icon
                                    :support-mix="supportMix"
                                    :support-eq-pieces="supportEqPieces"
                                    :search-by-root="showWarnTips(medicine)"
                                    data-cy="pr-chinese-medicine-autocomplete"
                                    @left="handleNameLeft(index)"
                                    @right="handleNameRight(index)"
                                    @up="handleNameUp(index)"
                                    @down="handleNameDown(index)"
                                    @focus="currentIndex = index"
                                    @closePanel="handleClosePanel(medicine, index)"
                                    @select="val => changeMedicine(val, index)"
                                    @dblclick.native="handleDoubleClick"
                                ></medicine-auto-complete>
                            </template>

                            <div
                                v-else
                                class="cadn"
                                :class="{ 'is-shortage': showWarnTips(medicine) }"
                                @click="editCurrentMedicine(index)"
                            >
                                <div
                                    :key="`${medicine.goodsId }_${ index}`"
                                    v-abc-goods-hover-popper:remote="{
                                        goods: medicine,
                                        onlyStock: true,
                                        pharmacyNo: medicine.pharmacyNo,
                                        openDelay: 500,
                                        dontShow: dragging,
                                        showStock: needCheckStock,
                                        showPrice: showDetailPrice,
                                        showCostPrice: canViewCostPrice,
                                        showShebaoCode: true,
                                        showPass: showPass
                                    }"
                                    class="name"
                                >
                                    {{ medicine.name }}
                                </div>

                                <div
                                    v-if="showWarnTips(medicine)"
                                    :data-tipsy="warnTips(medicine)"
                                    class="shortage-tips abc-tipsy abc-tipsy--n"
                                >
                                    <i class="iconfont cis-icon-Attention"></i>
                                </div>

                                <div
                                    v-if="medicine.verifySignatures && medicine.verifySignatures.length"
                                    class="doctor-sign"
                                    :class="{
                                        'is-float': medicine.name.length > 6
                                    }"
                                >
                                    <img v-if="doctorSignImgUrl" :src="doctorSignImgUrl" alt="" />
                                    <template v-else>
                                        {{ doctorName }}
                                    </template>
                                </div>


                                <div v-if="compareRepeat(medicine)" class="repeat-item">
                                    重复
                                </div>
                            </div>
                        </div>

                        <form-item-status v-if="form.chargeStatus" :item="medicine"></form-item-status>

                        <div v-if="isShowMedicalSort" class="show-index-wrapper">
                            {{ index + 1 }}
                        </div>

                        <div
                            v-if="!disabledForm && currentIndex !== index && medicine.name"
                            class="delete-icon-wrapper"
                        >
                            <delete-icon
                                data-cy="chinese-delete-icon"
                                @delete="deleteMedicine(index)"
                            ></delete-icon>
                        </div>

                        <div class="count-wrapper">
                            <abc-form-item
                                :required="!disabledForm && !!medicine.name"
                                :validate-event="validateNumberWithoutZero"
                            >
                                <abc-input
                                    ref="chineseMedicineInputs"
                                    v-model.number="medicine[getUnitCountField(medicine)]"
                                    v-abc-focus-selected
                                    placeholder="用量"
                                    :disabled="disabledForm"
                                    size="small"
                                    type="number"
                                    :input-custom-style="{
                                        padding: '3px 16px 3px 0', margin: '0 -1px 0 0'
                                    }"
                                    class="count"
                                    :max-length="6"
                                    :config="{
                                        formatLength: 2,
                                    }"
                                    data-cy="pr-chinese-count-input"
                                    @left="handleCountLeft(index)"
                                    @right="handleCountRight(index)"
                                    @up="handleCountUp(index)"
                                    @down="handleCountDown(index)"
                                    @focus="currentCountIndex = index"
                                    @blur="currentCountIndex = -1"
                                    @enter="enterEvent"
                                    @input="changeCount(medicine)"
                                >
                                </abc-input>
                                <span class="input-append-unit">{{ medicine.unit || 'g' }}</span>
                            </abc-form-item>
                        </div>

                        <abc-form-item
                            v-if="medicine.name && medicine.replaceList && medicine.replaceList.length"
                            class="chinese-replace-medicine"
                        >
                            点击可替换为<template
                                v-for="(replaceName, replaceIndex) in medicine.replaceList"
                            >
                                {{ replaceName }}{{ replaceIndex === medicine.replaceList.length - 1 ? '' : '、' }}
                            </template>
                        </abc-form-item>

                        <abc-form-item
                            v-else-if="medicine.name"
                            class="chinese-special-requirement"
                            :class="{
                                'show-input': showSpecialRequirement(medicine, index)
                            }"
                        >
                            <select-usage
                                ref="requirementInput"
                                v-model="medicine.specialRequirement"
                                :pr-form-item="medicine"
                                type="specialRequirement"
                                placeholder="煎法"
                                placement="bottom-start"
                                :can-change-charge-type="
                                    form.pharmacyType === PharmacyTypeEnum.LOCAL_PHARMACY ||
                                        form.pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY
                                "
                                :readonly="false"
                                :tabindex="-1"
                                :disabled="disabledForm"
                                :index="index"
                                :is-open-source="isOpenSource"
                                @left="handleRemarkLeft(index)"
                                @right="handleRemarkRight(index)"
                                @up="handleRemarkUp(index)"
                                @down="handleRemarkDown(index)"
                                @input="(val, el) => handleRemarkInput(medicine, el)"
                                @enter="enterEvent"
                                @change="selectChineseMedicineSpecial"
                            >
                            </select-usage>
                        </abc-form-item>

                        <abc-flex
                            v-if="showMedicalSpec(medicine) || medicine.passAuditStatus"
                            :gap="4"
                            align="center"
                            class="diff-spec"
                        >
                            <abc-text v-if="showMedicalSpec(medicine)" size="mini" theme="gray">
                                {{ medicine.productInfo | getSpec }}
                            </abc-text>
                            <pass-audit-dot
                                v-if="medicine.passAuditStatus"
                                :status="medicine.passAuditStatus"
                                :key-id="medicine.keyId"
                            ></pass-audit-dot>
                        </abc-flex>
                    </div>
                </transition-group>
            </draggable>
        </div>

        <div class="prescription-footer">
            <abc-space :size="12" class="chinese-description" data-cy="chinese-description">
                <div class="dose-count">
                    <div v-if="disabledForm && !!form.refundDoseCount">
                        {{ form.originDoseCount }}剂，退{{ form.refundDoseCount }}剂，余{{ form.doseCount }}剂
                    </div>
                    <abc-form-item
                        v-else
                        :key="validateDoseCountKey"
                        :required="needRequired && !disabledForm"
                        :validate-event="validateDoseCount"
                    >
                        <abc-input
                            ref="doseCount"
                            v-model="form.doseCount"
                            v-abc-focus-selected
                            class="count-center"
                            :width="82"
                            :input-custom-style="{ padding: '3px 21px 3px 6px' }"
                            :disabled="disabledForm"
                            :max-length="4"
                            type="number"
                            only-bottom-border
                            size="small"
                            data-cy="pr-chinese-doseCount-input"
                            @right="changeInputPointer('usages')"
                            @enter="enterEvent"
                            @input="handleDoseCountInput"
                        >
                        </abc-input>
                        <span class="input-append-unit">剂</span>
                    </abc-form-item>
                </div>

                <div class="usages">
                    <abc-form-item :required="needRequired && !disabledForm">
                        <select-usage
                            v-model="form.usage"
                            placeholder="用法"
                            :width="50"
                            :max-length="10"
                            size="small"
                            placement="bottom-start"
                            only-bottom-border
                            :readonly="isAirPharmacy"
                            :disabled="disabledForm"
                            :options="usageOptions"
                            data-cy="pr-chinese-usage-select"
                            @left="changeInputPointer('dose-count')"
                            @right="changeInputPointer(isSpecialUsages ? 'freq' : 'daily-dosage')"
                            @enter="enterEvent"
                            @change="selectChineseMedicineUsage"
                        >
                        </select-usage>
                    </abc-form-item>
                </div>

                <div v-if="!isSpecialUsages" class="daily-dosage">
                    <abc-form-item>
                        <abc-autocomplete
                            v-model="form.dailyDosage"
                            :width="70"
                            :inner-width="82"
                            :max-length="10"
                            placeholder="剂量"
                            only-bottom-border
                            :disabled="disabledForm"
                            :focus-show="true"
                            size="small"
                            :readonly="isAirPharmacy"
                            :fetch-suggestions="chineseMedicineDailyDosage"
                            :filter-suggestions="chineseMedicineDailyDosage"
                            data-cy="pr-chinese-dailyDosage-select"
                            @left="changeInputPointer('usages')"
                            @right="changeInputPointer('freq')"
                            @enter="enterEvent"
                            @enterEvent="selectChineseMedicineDailyDosage"
                            @change="$emit('queryVerify')"
                        >
                            <template slot="suggestions" slot-scope="props">
                                <dt
                                    class="suggestions-item"
                                    :class="{ selected: props.index === props.currentIndex }"
                                    data-cy="suggestions-item"
                                    @click="selectChineseMedicineDailyDosage(props.suggestion)"
                                >
                                    {{ props.suggestion.name }}
                                </dt>
                            </template>
                        </abc-autocomplete>
                    </abc-form-item>
                </div>

                <div class="freq">
                    <abc-form-item>
                        <select-usage
                            v-model="form.freq"
                            placeholder="频率"
                            :width="72"
                            :max-length="10"
                            size="small"
                            placement="bottom-start"
                            only-bottom-border
                            :readonly="isAirPharmacy"
                            :disabled="disabledForm"
                            :options="freqOptions"
                            data-cy="pr-chinese-freq-select"
                            @left="changeInputPointer('daily-dosage')"
                            @right="changeInputPointer('usage-level')"
                            @enter="enterEvent"
                            @input="calcUsageDays"
                            @change="selectChineseMedicineFreq"
                        >
                        </select-usage>
                    </abc-form-item>
                </div>

                <div class="usage-level">
                    <abc-form-item>
                        <select-usage
                            v-model="form.usageLevel"
                            placeholder="服用量"
                            :width="82"
                            :max-length="10"
                            size="small"
                            placement="bottom-start"
                            only-bottom-border
                            :readonly="isAirPharmacy"
                            :disabled="disabledForm"
                            :options="usageLevelOptions"
                            data-cy="pr-chinese-usageLevel-select"
                            @left="changeInputPointer('freq')"
                            @right="changeInputPointer(isSpecialUsages ? 'usage-days' : 'requirement')"
                            @enter="enterEvent"
                            @input="calcUsageDays"
                            @change="selectChineseMedicineUsageLevel"
                        >
                        </select-usage>
                    </abc-form-item>
                </div>

                <div v-if="isSpecialUsages" class="usage-days">
                    <abc-form-item>
                        <select-usage
                            v-model="form.usageDays"
                            placeholder="服用天数"
                            :width="70"
                            :inner-width="72"
                            :max-length="10"
                            size="small"
                            placement="bottom-start"
                            only-bottom-border
                            :readonly="false"
                            :disabled="disabledForm"
                            :options="chineseMedicineConfig.usageDays"
                            data-cy="pr-chinese-usageDays-select"
                            @left="changeInputPointer('usage-level')"
                            @right="changeInputPointer('requirement')"
                            @enter="enterEvent"
                            @change="selectChineseMedicineUsageDays"
                        >
                        </select-usage>
                    </abc-form-item>
                </div>

                <div class="requirement">
                    <abc-form-item>
                        <select-usage
                            v-model="form.requirement"
                            placeholder="备注"
                            :width="114"
                            :max-length="200"
                            size="small"
                            only-bottom-border
                            placement="bottom-start"
                            :readonly="false"
                            :disabled="disabledForm"
                            :options="requirementOptions"
                            need-hover-text
                            data-cy="pr-chinese-requirement-select"
                            @left="changeInputPointer(isSpecialUsages ? 'usage-days' : 'usage-level')"
                            @enter="enterEvent"
                            @change="selectChineseMedicineRequirement"
                        >
                        </select-usage>
                    </abc-form-item>
                </div>
                <abc-flex
                    v-if="form.verifySignatureStatus"
                    align="center"
                    class="doctor-sign"
                >
                    <img v-if="doctorSignImgUrl" :src="doctorSignImgUrl" alt="" />
                    <template v-else>
                        {{ doctorName }}
                    </template>
                </abc-flex>
            </abc-space>

            <abc-flex :gap="12" class="pr-info-status total-price" style="margin-left: auto;">
                <form-status v-if="disabled && form.medicinePriceInfo" :forms="[form.medicinePriceInfo]"></form-status>
                <div v-if="showTotalPrice" class="total-price">
                    <span v-if="!disabled"><abc-money
                        :value="formTotalPrice"
                        is-show-space
                        data-cy="pr-chinese-total-price"
                    ></abc-money></span>
                    <biz-charge-stat-popover
                        v-else
                        :title="`中药处方${translateH}`"
                        :forms="[form]"
                    >
                        <abc-money
                            :value="formTotalPrice"
                            is-show-space
                            data-cy="pr-chinese-total-price"
                        ></abc-money>
                    </biz-charge-stat-popover>
                </div>
            </abc-flex>
        </div>

        <div v-if="showProcessInfo || showDeliveryInfo" class="prescription-external">
            <div
                v-if="showProcessInfo"
                class="external-info-item"
            >
                <div>
                    <label>加工</label>
                    <div class="content ellipsis" data-cy="btn-process" @click="handleClickProcessInfo">
                        {{ processInfoStr }}
                    </div>
                </div>
                <div class="price">
                    <form-item-status
                        v-if="disabled && form.chargeStatus"
                        :item="form.processInfo"
                        refund-text="已退费"
                        style="margin-right: 10px;"
                    ></form-item-status>
                    <template v-if="showTotalPrice">
                        <span v-if="!disabled"><abc-money :value="processFee" is-show-space></abc-money></span>
                        <biz-charge-stat-popover
                            v-else
                            :title="`中药处方${translateH}`"
                            :forms="[form]"
                        >
                            <abc-money :value="processFee" is-show-space></abc-money>
                        </biz-charge-stat-popover>
                    </template>
                </div>
            </div>
            <div v-if="showDeliveryInfo" class="external-info-item">
                <div>
                    <label style="min-width: 32px;">快递</label>
                    <div class="content ellipsis" @click="showDelivery">
                        <p
                            v-if="canAddDelivery"
                            :style="{ color: $style.T3 }"
                        >
                            请填写收货地址
                        </p>
                        <div v-else>
                            <span>{{ deliveryCompany }}</span>
                            <abc-popover
                                v-if="form.deliveryInfo && form.deliveryInfo.deliveryOrderNo"
                                trigger="hover"
                                placement="bottom"
                                theme="white"
                                :visible-arrow="false"
                                style="display: inline-block;"
                                :popper-style="{ overflow: 'auto' }"
                            >
                                <span slot="reference" class="delivery-order-no">
                                    {{ form.deliveryInfo.deliveryOrderNo }}
                                </span>
                                <div class="chinese-prescription-delivery-detail">
                                    <p class="chinese-prescription-delivery-detail-header">
                                        {{ deliveryBriefInfo }}
                                    </p>
                                    <ul class="chinese-prescription-delivery-detail-content">
                                        <li
                                            v-for="(item, index) in form.deliveryInfo.traceList"
                                            :key="item.ftime"
                                            :class="{ yellow: index === 0 }"
                                        >
                                            <div>{{ item.context }}</div>
                                            <p>{{ item.ftime }}</p>
                                        </li>
                                    </ul>
                                </div>
                            </abc-popover>
                            <span>{{ deliveryAddressInfo }}</span>
                        </div>
                    </div>
                </div>
                <div class="price">
                    <form-item-status
                        v-if="disabled && form.chargeStatus"
                        :item="form.deliveryInfo"
                        refund-text="已退费"
                        style="margin-right: 10px;"
                    ></form-item-status>
                    <template v-if="showTotalPrice">
                        <span v-if="!disabled"><abc-money :value="deliveryFee" is-show-space></abc-money></span>
                        <biz-charge-stat-popover
                            v-else
                            :title="`中药处方${translateH}`"
                            :forms="[form]"
                        >
                            <abc-money :value="deliveryFee" is-show-space></abc-money>
                        </biz-charge-stat-popover>
                    </template>
                </div>
            </div>
        </div>

        <local-process-dialog
            v-if="showProcessDialog"
            v-model="showProcessDialog"
            :form="form"
            :process-remark="form.processRemark"
            :usage-type="form.usageType"
            :usage-sub-type="form.usageSubType"
            :process-bag-unit-count="form.processBagUnitCount"
            :total-process-count="form.totalProcessCount"
            :process-price="form.processInfo?.displayTotalPrice ?? 0"
            :ingredient-price="form.ingredientInfo?.displayTotalPrice ?? 0"
            :dose-count="form.doseCount"
            @confirm="confirmProcessHandler"
        ></local-process-dialog>

        <air-process-dialog
            v-if="showAirProcessDialog"
            v-model="showAirProcessDialog"
            :usage-scope-id="form.usageScopeId"
            :medicine-state-scope-id="form.medicineStateScopeId"
            :process-price="form.processInfo?.displayTotalPrice ?? 0"
            :ingredient-price="form.ingredientInfo?.displayTotalPrice ?? 0"
            :process-bag-unit-count="form.processBagUnitCount"
            :total-process-count="form.totalProcessCount"
            :process-remark="form.processRemark"
            :dose-count="form.doseCount"
            :pharmacy-type="form.pharmacyType"
            :process-bag-unit="processBagUnit"
            :quantity-configuration="quantityConfiguration"
            @confirm="confirmAirProcessHandler"
        ></air-process-dialog>

        <component
            :is="curCommonDialog"
            v-if="dialogVisible"
            v-model="dialogVisible"
            type="chinese"
            :prescription-form="form"
        ></component>

        <air-pharmacy-deliver-info
            v-if="showDeliverDialog"
            v-model="showDeliverDialog"
            scene="outpatient"
            :patient="patient"
            :delivery-info="form.deliveryInfo"
            :vendor-id="vendorInfo.vendorId"
            :usage-scope-id="form.usageScopeId"
            :specification="form.specification"
            :vendor-name="form.vendorName"
            :medicine-state-scope-id="form.medicineStateScopeId"
            :form="form"
            :air-pharmacy-forms="airPharmacyForms"
            :forms="forms"
            @confirm="deliverConfirmHandler"
        ></air-pharmacy-deliver-info>

        <dialog-delivery-virtual
            v-if="showDeliverVirtualDialog"
            v-model="showDeliverVirtualDialog"
            :pr-form="form"
            :patient="patient"
            :charge-status="form.chargeStatus"
            :delivery-info="form.deliveryInfo"
            @confirm="deliverConfirmHandler"
        ></dialog-delivery-virtual>

        <goods-select-dialog
            v-if="showGoodsSelectDialog"
            v-model="showGoodsSelectDialog"
            :default-category-key="defaultCategoryKey"
            :treat-online-clinic-id="treatOnlineClinicId"
            :department-id="departmentId"
            :category-range="categoryRange"
            @onSelectGoods="quickSelect"
        ></goods-select-dialog>
    </div>
</template>

<script type="text/ecmascript-6">
    // api
    import CDSSAPI from 'api/cdss/index';
    import ChargeAPI from 'api/charge';
    import OutpatientAPI from 'api/outpatient';
    import MessageAPI from 'api/message';
    import GoodsApi from 'api/goods/index';
    // vue lib
    import { mapGetters } from 'vuex';
    import Draggable from 'vuedraggable';
    import Big from 'big.js';
    // utils
    import {
        validateMobile, validateNumber, isDisabledGoods, validateNumberWithoutZero,
    } from 'utils/validate';
    import {
        numToChinese,
        getSafeNumber,
        createGUID,
    } from 'utils/index';
    import {
        calculateItemPriceUseRound, sum,
    } from 'utils/calculation';
    import clone from 'utils/clone';
    import { renewChineseMedicines } from './utils.js';
    import { windowOpen } from '@/core/navigate-helper';
    import {
        getPrescriptionItemStruct,
        trans2ChargeForm,
    } from 'views/layout/prescription/utils.js';
    import { getDefaultPharmacy } from 'views/common/pharmacy.js';
    import {
        clearFormBargainHandler, getItemUnitPrice,
    } from 'views/outpatient/utils.js';
    import { debounce } from 'utils/lodash.js';
    import { formatMoney } from 'src/filters/index';
    // components
    import DeleteIcon from '../../delete-icon/delete-icon';
    import MedicineAutoComplete from '../common/chinese-medicine-autocomplete';
    import AirPharmacyMedicineAutoComplete from '../common/air-pharmacy-medicine-autocomplete';
    import TrashButton from '../common/trash-button';
    import CommonPrescriptionDialog from '../common/common-prescription-dialog';
    import SelectUsage from '../../select-group/index.vue';
    import LocalProcessDialog from './local-process-dialog';
    import AirProcessDialog from './air-process-dialog';
    import VendorsPopover from 'src/views/air-pharmacy/vendors-popover';
    import JingMaDropdown from '../common/jing-ma-dropdown.vue';
    import AirPharmacyDeliverInfo from 'src/views/air-pharmacy/deliver-info.vue';
    import BizChargeStatPopover from '@/components-composite/biz-charge-stat-popover';
    import FormStatus from 'src/views/outpatient/common/form-status.vue';
    import FormItemStatus from 'src/views/outpatient/common/form-item-status.vue';
    import DialogDeliveryVirtual from './dialog-delivery-virtual.vue';
    import GoodsSelectDialog from 'views/layout/goods-select-dialog/index.vue';
    import { DetailImageDialog } from 'views/air-pharmacy/detail-image-dialog';
    import CommonPrescriptionHospitalDialog from 'views/layout/prescription/common/common-prescription-hospital-dialog.vue';
    import PassAuditDot from '@/pass-pharm-review/pass-audit-dot.vue';
    // constants
    import {
        getMedicineStateStrByScopeId,
        getUsageStrByScopeId,
        MedicineStateScopeIdEnum,
        UsageScopeIdEnum,
    } from 'src/views/air-pharmacy/constants.js';
    import {
        GoodsTypeIdEnum, PharmacyTypeEnum,
    } from '@abc/constants';
    import { OutpatientChargeTypeEnum } from 'views/outpatient/constants.js';
    import { ChargeFormStatusEnum } from '@/service/charge/constants.js';
    import { CmsResourceType } from '@/service/cms/constant';
    import { PROTOCOL } from 'utils/constants';
    import { CATEGORY_TYPE_ENUM } from 'views/common/goods-search/constants';
    import TrashButtonV2 from 'views/layout/prescription/common/trash-button-v2.vue';
    import { useAirPharmacy } from 'views/air-pharmacy/hooks/useAirPharmacy';
    import { EqConversionRuleEnum } from '@/common/constants/outpatient.js';
    import { isNotNull } from '@/utils';
    import NeiMengPassPR from '@/pass-pharm-review';

    const RepeatTypeEnum = Object.freeze({
        ALL: 1,
        ONLY_NAME: 2,
    });
    export default {
        components: {
            PassAuditDot,
            TrashButtonV2,
            GoodsSelectDialog,
            MedicineAutoComplete,
            AirPharmacyMedicineAutoComplete,
            DeleteIcon,
            TrashButton,
            Draggable,
            SelectUsage,
            LocalProcessDialog,
            AirProcessDialog,
            VendorsPopover,
            JingMaDropdown,
            AirPharmacyDeliverInfo,
            BizChargeStatPopover,
            FormStatus,
            FormItemStatus,
            DialogDeliveryVirtual,
        },
        inject: {
            outpatientEditForm: {
                default: null,
            },
        },
        props: {
            // 判断开出来源
            isOpenSource: {
                type: Boolean,
                default: false,
            },
            needCheckStock: {
                type: Boolean,
                default: true,
            },
            showDetailPrice: {
                type: Boolean,
                default: true,
            },
            showTotalPrice: {
                type: Boolean,
                default: true,
            },
            forms: {
                type: Array,
                default() {
                    return [];
                },
            },
            form: {
                type: Object,
                required: true,
            },
            formsLength: Number,
            formIndex: Number,
            status: Number,
            disabled: Boolean,
            disabledAdd: Boolean,
            patientInfo: Object,
            medicalRecord: Object,
            treatOnlineClinicId: [String, Number],
            shebaoCardInfo: {
                type: Object,
                default: null,
            },
            canChangePharmacyType: {
                type: Boolean,
                default: true,
            },
            templateManagerVersion: {
                type: Number,
                default: 0,
            },
            departmentId: String,
            isConsultation: Boolean,
        },
        setup() {
            const { applySuccessModal } = useAirPharmacy();
            return {
                applySuccessModal,
            };
        },
        data() {
            return {
                EqConversionRuleEnum,
                CATEGORY_TYPE_ENUM,
                PharmacyTypeEnum,
                commonPrescriptionName: '',

                currentIndex: -1,
                currentCountIndex: -1,

                showDeleteConfirm: false,
                dialogVisible: false,
                showProcessDialog: false,
                showAirProcessDialog: false,
                dragging: false,
                validateNumber,
                validateMobile,

                prescriptionTotal: 0,
                minimum: null,
                vendorAvailableMedicalStates: [],

                // 空中药方供应商
                vendorInfo: {
                    vendorUsageScopeId: this.form.vendorUsageScopeId,
                },
                // 快递
                showDeliverDialog: false,
                // 虚拟药房弹窗
                showDeliverVirtualDialog: false,
                CmsResourceType,
                showGoodsSelectDialog: false,
            };
        },
        computed: {
            ...mapGetters([
                'userInfo',
                'chineseMedicineConfig',
                'currentClinic',
                'clinicConfig',
                'chinesePRUsageDefault',
                'dispensingConfig',
                'allProcessUsages',
                'clinicBasic',
                'pharmacyRuleList',
                'isCanSeeGoodsCostPriceInOutpatient',
                'chainBasic',
            ]),
            ...mapGetters('airPharmacy', [
                'clinicCanUseAirPharmacy',
                'airPharmacyAdvertisementData',
            ]),
            enableEqCoefficient() {
                return this.chainBasic.enableEqCoefficient;
            },
            supportEqPieces() {
                if (!this.enableEqCoefficient) return false;
                const { eqConversionRule } = this.form;
                return this.showEqConversionRule && eqConversionRule === EqConversionRuleEnum.EQ_PIECES;
            },
            showEqConversionRule() {
                if (!this.enableEqCoefficient) return false;
                const {
                    pharmacyType, specification, eqConversionRule,
                } = this.form;
                return pharmacyType === PharmacyTypeEnum.LOCAL_PHARMACY &&
                    specification === '中药颗粒' &&
                    isNotNull(eqConversionRule);
            },

            showPass() {
                return NeiMengPassPR.needPass;
            },

            defaultCategoryKey() {
                if (this.form.specification === '中药颗粒') {
                    return CATEGORY_TYPE_ENUM.MEDICINE_CHINESE_GRANULE;
                }
                return CATEGORY_TYPE_ENUM.MEDICINE_CHINESE_PIECES;
            },

            categoryRange() {
                if (this.supportMix) {
                    return [
                        CATEGORY_TYPE_ENUM.MEDICINE_CHINESE_PIECES,
                        CATEGORY_TYPE_ENUM.MEDICINE_CHINESE_GRANULE,
                    ];
                }
                if (this.form.specification === '中药颗粒') {
                    return [CATEGORY_TYPE_ENUM.MEDICINE_CHINESE_GRANULE];
                }
                return [CATEGORY_TYPE_ENUM.MEDICINE_CHINESE_PIECES];
            },

            supportMix() {
                return this.form.pharmacyType === PharmacyTypeEnum.LOCAL_PHARMACY && this.chinesePrescriptionSupportMix;
            },

            airPharmacyAdvertisement() {
                return this.airPharmacyAdvertisementData.airPharmacyAdvertisement || [];
            },
            piecesDefaultPharmacy() {
                return getDefaultPharmacy(this.pharmacyRuleList,{
                    departmentId: this.departmentId,
                    goodsInfo: {
                        typeId: GoodsTypeIdEnum.MEDICINE_CHINESE_PIECES,
                    },
                    processInfo: {
                        usageType: this.form.usageType,
                        usageSubType: this.form.usageSubType,
                    },
                });
            },
            granuleDefaultPharmacy() {
                return getDefaultPharmacy(this.pharmacyRuleList, {
                    departmentId: this.departmentId,
                    goodsInfo: {
                        typeId: GoodsTypeIdEnum.MEDICINE_CHINESE_GRANULE,
                    },
                    processInfo: {
                        usageType: this.form.usageType,
                        usageSubType: this.form.usageSubType,
                    },
                });
            },


            /**
             * 医生能否查看成本价
             */
            canViewCostPrice() {
                return this.isCanSeeGoodsCostPriceInOutpatient;
            },

            curCommonDialog() {
                if (this.templateManagerVersion === 1) return CommonPrescriptionHospitalDialog;
                return CommonPrescriptionDialog;
            },

            chinesePrescriptionSupportMix() {
                const {
                    settings,
                } = this.clinicBasic.outpatient;
                // 中药支持 饮片颗粒混开
                return settings.chinesePrescriptionSupportMix;
            },

            doctorSignImgUrl() {
                const { doctorSignImgUrl = '' } = this.outpatientEditForm || {};
                if (doctorSignImgUrl) return doctorSignImgUrl;
                const {
                    id: userId,
                    handSign,
                    handSignType,
                } = this.userInfo || {};
                if (handSignType === 1 && this.doctorId === userId && handSign) {
                    return handSign;
                }
                return '';
            },
            doctorId() {
                const { postData = {} } = this.outpatientEditForm || {};
                return postData.doctorId || '';
            },
            doctorName() {
                const { postData = {} } = this.outpatientEditForm || {};
                return postData.doctorName || '';
            },

            patient() {
                const { postData = {} } = this.outpatientEditForm || {};
                return postData.patient || {};
            },

            // 空处方删除不需要确认弹窗
            deleteNeedConfirm() {
                return this.medicineList.some((item) => {
                    return item.name;
                });
            },

            requirementOptions() {
                return this.chineseMedicineConfig.requirement.slice();
            },

            translateH() {
                if (this.formsLength === 1) return '';
                return numToChinese(this.formIndex + 1);
            },

            /**
             * @desc 能否切换药房类型
             * <AUTHOR> Yang
             * @date 2020-07-30 18:17:19
             */
            showChangePharmacyType() {
                return this.isOpenSource && this.canChangePharmacyType;

            },

            showDecoction() {
                return this.form.chargeStatus >= 1 || (this.dispensingConfig && this.dispensingConfig.isDecoction);
            },
            disabledForm() {
                return this.disabled;
            },
            needRequired() {
                return !!this.form.prescriptionFormItems.filter((item) => item.goodsId || item.name).length;
            },

            totalWeight() {
                let count = 0;
                this.medicineList.forEach((item) => {
                    if (item.name && item[this.getUnitCountField(item)]) {
                        if (item.goodsId && item.unit === 'g') {
                            count += +item[this.getUnitCountField(item)] || 0;
                        }
                    }
                });
                return count * (this.form.doseCount || 0);
            },
            /**
             * @desc 显示显示重量信息
             * <AUTHOR>
             * @date 2019/11/15 10:56:40
             */
            totalWeightInfo() {
                const kinds = [];
                let count = 0;
                let totalCount = 0;
                let str = '';

                this.medicineList.forEach((item) => {
                    if (item.name && item[this.getUnitCountField(item)]) {
                        if (item.goodsId && item.unit === 'g') {
                            count += +item[this.getUnitCountField(item)] || 0;
                        }
                        if (kinds.indexOf(item.name) === -1) {
                            kinds.push(item.name);
                        }
                    }
                });

                if (kinds.length) {
                    totalCount = count * (this.form.doseCount || 0);

                    count = Number(count.toFixed(2));
                    totalCount = Number(totalCount.toFixed(2));

                    str += `${kinds.length} 味, 单剂 ${count}g, 总计 ${totalCount}g`;

                }
                return {
                    desc: str,
                    totalCount,
                };
            },

            formTotalPrice() {
                const { medicinePriceInfo } = this.form;
                const {
                    chargeStatus,
                    receivedPrice,
                    receivableFee,
                    refundedFee,
                } = medicinePriceInfo || {};

                // 全收（包含退单、退费、部分退）显示实收，未收、部分收显示原价
                // form 上已收、部分收、部分退状态都是1，如果(应收 === 实收 + 已退)就是全收了
                if (
                    chargeStatus > ChargeFormStatusEnum.CHARGED ||
                    (
                        chargeStatus === ChargeFormStatusEnum.CHARGED &&
                        sum(receivableFee) === sum(receivedPrice, Math.abs(refundedFee))
                    )
                ) {
                    return receivedPrice;
                }

                let prescriptionTotal = this.form.expectedTotalPrice || this.prescriptionTotal;
                prescriptionTotal = +prescriptionTotal || 0;
                return prescriptionTotal || 0;
            },

            processFee() {
                const {
                    processInfo,
                    processPrice,
                    ingredientInfo,
                    ingredientPrice,
                } = this.form;

                if (processInfo || ingredientInfo) {
                    const { displayTotalPrice: processDisplayPrice } = processInfo || {};
                    const { displayTotalPrice: ingredientDisplayPrice } = ingredientInfo || {};
                    return sum(processDisplayPrice || 0, ingredientDisplayPrice || 0);
                }

                return sum(processPrice || 0, ingredientPrice || 0);
            },

            deliveryFee() {
                const { deliveryInfo } = this.form;
                const {
                    deliveryPrice,
                    displayTotalPrice,
                } = deliveryInfo || {};
                return displayTotalPrice || deliveryPrice || 0;
            },

            medicineList: {
                get() {
                    return this.form.prescriptionFormItems;
                },
                set(val) {
                    this.form.prescriptionFormItems = val;
                },
            },
            // 特殊的用法，需要增加服用天数字段，隐藏剂量字段
            isSpecialUsages() {
                const _arr = ['制膏', '制丸', '打粉'];
                return _arr.indexOf(this.form.usage) > -1;
            },

            // 空中药方
            isAirPharmacy() {
                const { pharmacyType } = this.form;
                return pharmacyType === PharmacyTypeEnum.AIR_PHARMACY;
            },

            airPharmacyForms() {
                return this.forms.filter((x) => x.pharmacyType === PharmacyTypeEnum.AIR_PHARMACY);
            },

            usageOptions() {
                if (this.isAirPharmacy) {
                    const { businessScopeConfig } = this.vendorInfo;
                    const { usageOptions } = businessScopeConfig || {};
                    return usageOptions || [];
                }
                return this.chineseMedicineConfig?.usages || [];
            },

            freqOptions() {
                if (this.isAirPharmacy) {
                    const { businessScopeConfig } = this.vendorInfo;
                    const { freqOptions } = businessScopeConfig || {};
                    return freqOptions || [];
                }
                return this.chineseMedicineConfig?.freq || [];
            },

            usageLevelOptions() {
                if (this.isAirPharmacy) {
                    const { businessScopeConfig } = this.vendorInfo;
                    const { usageLevelOptions } = businessScopeConfig || {};
                    return usageLevelOptions || [];
                }
                const {
                    usageLevel,
                    zhiGaoUsageLevel,
                    zhiWanUsageLevel,
                    daFenUsageLevel,
                    keLiChongFuUsageLevel,
                } = this.chineseMedicineConfig;

                if (this.form.specification?.indexOf('颗粒') > -1 && this.form.usage === '冲服') {
                    return keLiChongFuUsageLevel;
                }

                switch (this.form.usage) {
                    case '制膏':
                        return zhiGaoUsageLevel;
                    case '制丸':
                        return zhiWanUsageLevel;
                    case '打粉':
                        return daFenUsageLevel;
                    default:
                        return usageLevel;
                }
            },

            /**
             * 颗粒剂可调剂的数量配置
             * doseCountOption 0 自动计算 1 固定数量
             * bagUnitCount 调剂数量
             * bagUnit 调剂单位
             * @returns {{bagUnit: string, bagUnitCount: number, doseCountOption: number}}
             */
            quantityConfiguration() {
                let doseCountOption = 0, bagUnitCount = 1, bagUnit = '袋';
                if (this.isAirPharmacy) {
                    const {
                        calculateProcessBagType, processBagUnitCount, processBagUnit,
                    } = this.vendorInfo;
                    doseCountOption = calculateProcessBagType ?? 0;
                    bagUnitCount = processBagUnitCount ?? 1;
                    bagUnit = processBagUnit ?? '袋';
                }
                return {
                    doseCountOption, bagUnitCount, bagUnit,
                };
            },

            // 颗粒剂开方是否只能开双数 0 无限制 1 只能开双数
            isDualNumber() {
                if (this.isAirPharmacy) {
                    const { doseCountLimit } = this.vendorInfo;
                    return doseCountLimit ?? 0;
                }
                return 0;
            },

            /**
             * @desc 单位为g的一剂总重量
             * <AUTHOR>
             * @date 2021-04-26 10:59:27
             * @params
             * @return
             */
            doseTotalWeight() {
                return this.medicineList.reduce((arr, cur) => {
                    if (cur.medicineCadn && cur[this.getUnitCountField(cur)] && cur.unit === 'g') {
                        arr = arr + (+cur[this.getUnitCountField(cur)]);
                    }
                    return arr;
                }, 0);
            },

            /**
             * @desc 针对空中药房切换用法会更新 validateDoseCount 函数，这里用key来做
             * <AUTHOR>
             * @date 2021-04-27 19:56:51
             * @params
             * @return
             */
            validateDoseCountKey() {
                const {
                    pharmacyType, usageScopeId, medicineStateScopeId,
                } = this.form;
                if (pharmacyType === PharmacyTypeEnum.LOCAL_PHARMACY) return true;
                return `${usageScopeId}${medicineStateScopeId}${this.doseTotalWeight}`;
            },

            /**
             * @desc 是饮片代煎
             * <AUTHOR>
             * @date 2023-08-08 11:25:59
             */
            isYPDaiJian() {
                const {
                    specification,
                    medicineStateScopeId,
                } = this.form;
                return specification === '中药饮片' &&
                    medicineStateScopeId === MedicineStateScopeIdEnum.daiJian;
            },

            /**
             * @desc 包含加工
             * 1.本地药房打开了加工开关
             * 2.非自煎的空中药房 都有加工费
             * 3.代煎代配药房：若为饮片处方-代煎，展示加工信息、快递信息
             * <AUTHOR>
             * @date 2023-08-08 11:26:53
             */
            showProcessInfo() {
                const {
                    pharmacyType, // 药房
                    isDecoction,
                    medicineStateScopeId,
                } = this.form;
                if (pharmacyType === PharmacyTypeEnum.LOCAL_PHARMACY) {
                    return isDecoction;
                }
                if (pharmacyType === PharmacyTypeEnum.AIR_PHARMACY) {
                    // 非自煎的空中药房 都有加工费
                    return medicineStateScopeId !== MedicineStateScopeIdEnum.ziJian;
                }
                if (pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY) {
                    // * 代煎代配药房：若为饮片处方-代煎，展示加工信息、快递信息
                    return this.isYPDaiJian;
                }
                return false;
            },
            /**
             * @desc 包含快递
             * 1.空中药房
             * 2.代煎代配药房：若为饮片处方-代煎，展示加工信息、快递信息
             * <AUTHOR>
             * @date 2023-08-08 11:26:53
             */
            showDeliveryInfo() {
                if (this.isAirPharmacy || this.form.pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY) {
                    if (!this.disabled) return true;
                    const { deliveryInfo } = this.form;
                    const { deliveryCompany } = deliveryInfo || {};
                    return !!deliveryCompany?.id;
                }
                return false;
            },
            /**
             * @desc 加工展示文案，空中药房，本地药房展示规则区分
             * <AUTHOR>
             * @date 2022-06-16 16:02:13
             */
            processInfoStr() {
                const {
                    pharmacyType,
                    processBagUnitCount = 1,
                    totalProcessCount,

                    usageType,
                    usageSubType,

                    usageScopeId,
                    medicineStateScopeId,
                    processInfo,
                    ingredientInfo,

                } = this.form;
                if (pharmacyType === PharmacyTypeEnum.LOCAL_PHARMACY) {

                    const usageTypeInfo = this.allProcessUsages.find((it) => it.type === usageType);
                    if (!usageTypeInfo) return '';

                    const _arr = [];

                    const usageSubTypeInfo = usageTypeInfo.children.find((it) => it.subType === usageSubType);
                    if (usageSubTypeInfo) {
                        _arr.push(usageSubTypeInfo.name);
                    } else {
                        _arr.push(usageTypeInfo.name);
                    }
                    // 加工方式为煎药
                    if (usageType === 1) {
                        if (processBagUnitCount) {
                            _arr.push(`1剂煎 ${processBagUnitCount} 袋`);
                        }
                        if (totalProcessCount) {
                            _arr.push(`共 ${totalProcessCount} 袋`);
                        }
                    }
                    _arr.push(`加工费 ${this.$t('currencySymbol')} ${formatMoney(this.processFee)}`);

                    return _arr.join('，');
                }

                if (pharmacyType === PharmacyTypeEnum.AIR_PHARMACY) {
                    const _arr = [getUsageStrByScopeId(usageScopeId)];
                    if (usageScopeId === UsageScopeIdEnum.jianYao) {
                        if (processBagUnitCount) {
                            _arr.push(`，1剂煎 ${processBagUnitCount} 袋`);
                        }
                        if (totalProcessCount) {
                            _arr.push(`，共 ${totalProcessCount} 袋`);
                        }
                    } else if (usageScopeId === UsageScopeIdEnum.keLi) {
                        let unit = this.processBagUnit, unitCount = processBagUnitCount;
                        if (this.quantityConfiguration.doseCountOption) {
                            unit = this.quantityConfiguration.bagUnit ?? unit;
                            unitCount = this.quantityConfiguration.bagUnitCount ?? unitCount;
                        }
                        if (unitCount) {
                            _arr.push(`，1剂调 ${unitCount} ${unit}`);
                        }
                    } else {
                        _arr.push(`（${getMedicineStateStrByScopeId(medicineStateScopeId)}）`);
                    }
                    if (ingredientInfo) {
                        const ingredientFee = ingredientInfo.displayTotalPrice ?? 0;
                        _arr.push(`，辅料费 ${this.$t('currencySymbol')} ${formatMoney(ingredientFee)}`);
                    }
                    if (processInfo) {
                        const processFee = processInfo.displayTotalPrice ?? 0;
                        _arr.push(`，加工费 ${this.$t('currencySymbol')} ${formatMoney(processFee)}`);
                    }
                    return _arr.join('');
                }

                if (pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY) {
                    const _arr = ['煎药'];
                    if (processBagUnitCount) {
                        _arr.push(`1剂煎 ${processBagUnitCount} 袋`);
                    }
                    if (totalProcessCount) {
                        _arr.push(`共 ${totalProcessCount} 袋`);
                    }
                    return _arr.join('，');
                }

                return '';
            },

            /**
             * @desc 空中药房 成品率提示
             * <AUTHOR>
             * @date 2022-06-16 16:02:47
             */
            airPharmacyTotalTips() {
                const {
                    pharmacyType, // 药房
                    specification, // 饮片、颗粒
                    usageScopeId, // 煎药、制膏、制丸、打粉
                    finishedRateMin, // 成品率范围下限
                    finishedRate, // 成品率范围上限
                } = this.form;
                const { totalCount } = this.totalWeightInfo; // 总重量

                if (pharmacyType !== PharmacyTypeEnum.AIR_PHARMACY) return '';
                if (specification !== '中药饮片') return '';

                const produceCountMin = (totalCount * finishedRateMin).toFixed(1); // 成品最低重量
                const produceCountMax = (totalCount * finishedRate).toFixed(1); // 成品最高重量
                const finishedRange = `${produceCountMin}g~${produceCountMax}g`; // 成品范围
                const finishedRateRange = `${~~(finishedRateMin * 100)}%~${~~(finishedRate * 100)}%`; // 成品率范围

                if (usageScopeId === UsageScopeIdEnum.zhiGao) {
                    return `预计成品总重${finishedRange}（出膏量约为药材总重的${finishedRateRange}）`;
                }
                if (usageScopeId === UsageScopeIdEnum.daFen) {
                    return `预计成品总重${finishedRange}（打粉成品约为药材总重的${finishedRateRange}）`;
                }
                if (usageScopeId === UsageScopeIdEnum.zhiWan) {
                    return `预计成品总重${finishedRange}（制丸成品约为药材总重的${finishedRateRange}）`;
                }
                return '';
            },

            /**
             * @desc 本地药房成品率提示
             */
            otherPharmacyTotalTips() {
                const {
                    pharmacyType, // 药房
                    usage,
                } = this.form;
                if (pharmacyType === PharmacyTypeEnum.AIR_PHARMACY) return '';
                const { totalCount } = this.totalWeightInfo; // 总重量
                let produceCount = 0;
                switch (usage) {
                    case '制膏':
                        produceCount = (totalCount * 0.4).toFixed(1);
                        return `预计成品总重${produceCount}g（出膏量约为药材总重的40%）`;
                    case '制丸':
                        produceCount = (totalCount * 0.8).toFixed(1);
                        return `预计成品总重${produceCount}g（制丸成品约为药材总重的80%）`;
                    case '打粉':
                        produceCount = (totalCount * 0.8).toFixed(1);
                        return `预计成品总重${produceCount}g（打粉成品约为药材总重的80%）`;
                    default:
                        return '';
                }
            },
            /**
             * @desc 中药处方药品显示序号满足：1、chain 维度的配置中开启，2、本地药房
             * <AUTHOR>
             * @date 2022/08/26 16:28:54
             */
            isShowMedicalSort() {
                return this.clinicBasic.isChinesePrescriptionShowSort && this.form.pharmacyType === PharmacyTypeEnum.LOCAL_PHARMACY;
            },

            canAddDelivery() {
                const {
                    deliveryInfo,
                } = this.form;
                const {
                    deliveryCompany,
                } = deliveryInfo || {};
                const { id } = deliveryCompany || {};
                return !id && !this.disabledForm;
            },

            deliveryCompany() {
                const {
                    deliveryInfo,
                } = this.form;
                const {
                    deliveryCompany, deliveryPayType,
                } = deliveryInfo || {};
                const { name } = deliveryCompany || {};
                return name ? `${name}·${deliveryPayType ? '寄付' : '到付'}·` : '';
            },
            deliveryBriefInfo() {
                const {
                    deliveryInfo,
                } = this.form;
                const {
                    deliveryCompany, deliveryOrderNo,
                } = deliveryInfo || {};
                const { name } = deliveryCompany || {};
                return `${name}: ${deliveryOrderNo}`;
            },
            deliveryAddressInfo() {
                const {
                    deliveryInfo,
                } = this.form;
                const {
                    deliveryOrderNo,
                    addressProvinceName,
                    addressCityName,
                    addressDistrictName,
                    addressDetail,
                } = deliveryInfo || {};
                const address = `${addressProvinceName || ''}${addressCityName || ''}${addressDistrictName || ''}${addressDetail || ''}`;
                if (deliveryOrderNo) {
                    return `·${address}`;
                }
                return address;
            },
            processBagUnit() {
                if (this.disabled && this.form.processBagUnit) {
                    return this.form.processBagUnit;
                }
                let unit = '袋';
                if (this.form.usageLevel && typeof this.form.usageLevel === 'string') {
                    unit = this.form.usageLevel[this.form.usageLevel.length - 1];
                }
                return unit;
            },
            // 空中药房推广语
            airPharmacyPromotion() {
                const promotion = this.airPharmacyAdvertisement.find((item) => item.type === CmsResourceType.AIR_PHARMACY_PROMOTION);
                return promotion ?? {};
            },
            // 空中药房顶部广告语
            airPharmacyTopSlogan() {
                const topSlogan = this.airPharmacyAdvertisement.find((item) => item.type === CmsResourceType.AIR_PHARMACY_TOP_SLOGAN);
                return topSlogan ?? {};
            },
            airPharmacyActivateAdvertising() {
                const activateAdvertising = this.airPharmacyAdvertisement.find((item) => item.type === CmsResourceType.AIR_PHARMACY_ACTIVATE_ADVERTISING);
                return activateAdvertising ?? {};
            },

        },
        watch: {
            totalWeight() {
                // 重量变化需要重新计算快递费
                this.calcDeliveryFee();
            },
            quantityConfiguration: {
                handler(v) {
                    if (this.disabled) return;
                    if (v && v.doseCountOption === 1 && v.bagUnit) {
                        this.$set(this.form, 'processBagUnit', v.bagUnit);
                    } else {
                        this.$set(this.form, 'processBagUnit', this.processBagUnit);
                    }
                },
                deep: true,
            },
            processBagUnit: {
                handler(v) {
                    if (this.disabled) return;
                    if (this.quantityConfiguration && this.quantityConfiguration.doseCountOption === 1 && this.quantityConfiguration.bagUnit) {
                        this.$set(this.form, 'processBagUnit', this.quantityConfiguration.bagUnit);
                    } else {
                        this.$set(this.form, 'processBagUnit', v);
                    }
                },
            },
            showEqConversionRule(val) {
                if (val) {
                    this.$set(this.form, 'eqConversionRule', EqConversionRuleEnum.EQ_PIECES);
                } else {
                    this.$set(this.form, 'eqConversionRule', null);
                }
            },
        },
        async created() {
            this.$store.dispatch('initAllProcessUsages');
            if (!this.disabledForm) {
                this.insertAfterCM();
            }

            /**
             * @desc 本地药房需要前端计算总价
             * <AUTHOR> Yang
             * @date 2020-07-31 14:48:31
             */
            this.$watch('form.prescriptionFormItems', () => {
                this._calcFormTotalPrice();
            }, {
                deep: true,
            });

            this.$watch('form.medicineStateScopeId', () => {
                this.setIsDecoction();
            });

            this.calcFormTotalPrice();

            this._calcVirtualPharmacyDeliveryFee = debounce(this.calcVirtualPharmacyDeliveryFee, 500, true);
            this._calcFormTotalPrice = debounce(this.calcFormTotalPrice, 400, true);
            this._handleDoseCountChange = debounce(this.handleDoseCountChange, 400, true);
            this.fetchDeliveryTrace();

            // hack 后端接收的是deliveryCompany，但是返回deliveryCompany.id
            const { deliveryInfo } = this.form;
            const {
                deliveryCompanyId, deliveryCompany,
            } = deliveryInfo || {};
            if (!deliveryCompanyId && deliveryCompany && deliveryCompany.id) {
                this.form.deliveryInfo.deliveryCompanyId = deliveryCompany.id;
            }

            if (!this.airPharmacyAdvertisementData.isInit) {
                this.$store.dispatch('airPharmacy/fetchAirPharmacyAdvertisementData');
            }
            this.$abcEventBus.$on('outpatient-fee-change', this.handleCopyPR, this);
        },
        beforeDestroy() {
            this._timer && clearTimeout(this._timer);
            this.$abcEventBus.$offVmEvent(this._uid);
        },
        methods: {
            validateNumberWithoutZero,
            /**
             * @desc 当处方上的规格和药品不一致需要展示规格
             * <AUTHOR>
             * @date 2022-10-12 14:32:29
             */
            showMedicalSpec(item) {
                const {
                    specification,
                } = this.form;
                const medicineSpec = item.cMSpec || (item.productInfo && item.productInfo.cMSpec);
                return specification !== medicineSpec;
            },
            showSpecialRequirement(medicine, index) {
                return medicine.specialRequirement ||
                    this.currentIndex === index ||
                    this.currentCountIndex === index;
            },
            calcFormTotalPrice() {
                const _arr = [];
                const {
                    prescriptionFormItems,
                    doseCount = 0,
                } = this.form;

                prescriptionFormItems.forEach((it) => {
                    if (it.chargeType !== OutpatientChargeTypeEnum.NO_CHARGE) {
                        const {
                            unitCount = 0,
                            fractionPrice = 0,
                        } = it;
                        // 有议价情况直接用currentUnitPrice，没有用unitPrice 计算
                        const unitPrice = getItemUnitPrice(it);
                        _arr.push(calculateItemPriceUseRound(unitPrice, unitCount, doseCount));
                        _arr.push(fractionPrice || 0);
                    }
                });
                this.prescriptionTotal = sum(..._arr);
            },

            handleDoseCountInput() {
                this._handleDoseCountChange();
            },

            async handleDoseCountChange() {
                this.form.prescriptionFormItems.forEach((item) => {
                    item.doseCount = this.form.doseCount;
                    if (item.expectedTotalPrice) {
                        item.expectedTotalPrice = null;
                        if (item.sourceUnitPrice !== item.unitPrice) {
                            item.expectedUnitPrice = item.unitPrice;
                        }
                    }
                });
                // 修改剂量后需要清空议价
                const hasBargain = clearFormBargainHandler(this.form);
                if (hasBargain) {
                    this.$Toast({
                        message: '价格变动导致议价清空，请重新确认价格',
                        type: 'error',
                        duration: 2000,
                        referenceEl: this.$refs.doseCount.$el,
                    });
                }
                this.clearMedicineUsageRuleSignatures();
                this.$emit('queryVerify');
                await this.calcBagsNumber();
                this.outpatientFeeChange();
                this.calcUsageDays();
                this.formCalcFee();
            },
            validateDoseCount(value, callback) {
                const {
                    pharmacyType,
                    doseCount,
                    specification,
                    medicineStateScopeId,
                } = this.form;
                // 空中药房才做以下验证
                if (pharmacyType !== PharmacyTypeEnum.AIR_PHARMACY) {
                    callback({
                        validate: true,
                    });
                    return;
                }
                // 中药饮片 + 代煎 剂量至少为 3
                if (specification === '中药饮片' &&
                    medicineStateScopeId === MedicineStateScopeIdEnum.daiJian) {
                    if (value < 3) {
                        callback({
                            validate: false,
                            message: '3剂起煎',
                        });
                    }
                    return;
                }
                const mixDoseCounts = doseCount * this.doseTotalWeight;

                // 单位为 g 判断起做量
                if (mixDoseCounts < +this.minimum) {
                    callback({
                        validate: false,
                        message: `起做量${+this.minimum}g`,
                    });
                    return;
                }

                // 如果为颗粒剂,且开启了双数剂数,则剂数不能为非双方深化苏
                if (medicineStateScopeId === MedicineStateScopeIdEnum.keLi && this.isDualNumber && value % 2 !== 0) {
                    callback({
                        validate: false,
                        message: '需剂数为双数才可调剂',
                    });
                    return;
                }

                callback({
                    validate: true,
                });
            },
            /**
             * @desc 饮片=》煎服，颗粒=》冲服
             * <AUTHOR>
             * @date 2020/04/03 17:28:25
             * @params
             * @return
             */
            submitChangeSpecification(newSpecification, disableChangeUsage) {
                if (newSpecification) {
                    this.form.specification = newSpecification;
                }
                if (!disableChangeUsage) {
                    if (this.form.specification.indexOf('颗粒') > -1) {
                        if (this.form.usage !== '冲服') {
                            this.$nextTick(() => {
                                this.selectChineseMedicineUsage('冲服');
                            });
                        }
                    } else if (this.form.specification.indexOf('饮片') > -1) {
                        if (this.form.usage !== '煎服') {
                            this.$nextTick(() => {
                                this.selectChineseMedicineUsage('煎服');
                            });
                        }
                    }
                }
                this.$nextTick(() => {
                    this.calcBagsNumber();
                    this.outpatientFeeChange();
                });

                // 不是本地药房不做后续操作
                if (this.form.pharmacyType !== PharmacyTypeEnum.LOCAL_PHARMACY) {
                    // 非本地药房切换规格，修改药房后需要清空议价
                    const hasBargain = clearFormBargainHandler(this.form);
                    if (hasBargain) {
                        this.$Toast({
                            message: '价格变动导致议价清空，请重新确认价格',
                            type: 'error',
                            duration: 2000,
                            referenceEl: this.$refs.specSelect.$el,
                        });
                    }
                    return false;
                }

                // 获取本地默认药房号
                const defaultPharmacy = newSpecification === '中药颗粒' ? this.granuleDefaultPharmacy : this.piecesDefaultPharmacy;
                Object.assign(this.form, {
                    pharmacyType: defaultPharmacy.type,
                    pharmacyNo: defaultPharmacy.no,
                    pharmacyName: defaultPharmacy.name,
                });
            },
            changeSpecification(newSpecification, disableChangeUsage, oldSpecification) {
                if (newSpecification === oldSpecification) return;
                const _diffs = [];
                this.medicineList.forEach((item) => {
                    if (item.name) {
                        const cMSpec = item.cMSpec || (item.productInfo && item.productInfo.cMSpec);
                        if (cMSpec !== newSpecification) {
                            _diffs.push(item.name);
                        }
                    }
                });
                if (_diffs.length) {
                    const newSpec = newSpecification.replace('中药', '');

                    this.$confirm({
                        title: `处方药态转换为${newSpec}`,
                        content: [
                            `是否将处方中的所有药品转换为${newSpec}？`,
                        ],
                        customClass: 'change-spec-confirm-dialog',
                        contentStyles: 'width: 360px;padding: 24px',
                        confirmText: '确认转换',
                        onConfirm: async () => {
                            await this.change2Local(newSpecification);
                            this.submitChangeSpecification(newSpecification, disableChangeUsage);
                        },
                        onCancel: () => {
                            this.form.specification = oldSpecification;
                            this.initEqConversionRule(oldSpecification);
                        },
                    });
                } else {
                    this.submitChangeSpecification(newSpecification, disableChangeUsage);
                    this.initEqConversionRule(newSpecification);
                }
            },

            initEqConversionRule(specification = this.form.specification) {
                if (!this.enableEqCoefficient) return;
                if (this.form.pharmacyType !== PharmacyTypeEnum.LOCAL_PHARMACY) {
                    this.$set(this.form, 'eqConversionRule', null);
                    return;
                }
                if (specification === '中药颗粒') {
                    this.$set(this.form, 'eqConversionRule', EqConversionRuleEnum.EQ_PIECES);
                } else {
                    this.$set(this.form, 'eqConversionRule', null);
                }
            },

            changePharmacyType() {
                this.$set(this.form, 'finishedRateMin', null);
                this.$set(this.form, 'finishedRate', null);
                this.$emit('queryVerify');
                this.initEqConversionRule(this.form.specification);
            },

            changeCount(medicine) {
                if (medicine.expectedTotalPrice) {
                    medicine.expectedTotalPrice = null;
                    if (medicine.sourceUnitPrice !== medicine.unitPrice) {
                        medicine.expectedUnitPrice = medicine.unitPrice;
                    }
                }
                this.clearMedicineUsageRuleSignatures(medicine);
                this.$emit('queryVerify');
                this.calcUsageDays();
                this.$emit('add-prescription-form-item', medicine);
                this.outpatientFeeChange();
            },

            /**
             * @desc 修改药品数量后，需要清空对于超剂量提示的签名
             * <AUTHOR>
             * @date 2022-06-13 09:02:51
              */
            clearMedicineUsageRuleSignatures(medicine) {
                if (medicine) {
                    this.medicineList.forEach((item) => {
                        if (medicine.keyId === item.keyId && item.verifySignatures) {
                            item.verifySignatures = item.verifySignatures.filter((it) => {
                                return it.keyId !== item.keyId;
                            });
                        }
                    });
                } else {
                    this.medicineList.forEach((item) => {
                        if (item.verifySignatures) {
                            item.verifySignatures = item.verifySignatures.filter((it) => {
                                return it.keyId !== item.keyId;
                            });
                        }
                    });
                }
            },

            isLastRow(index) {
                const len = this.medicineList.length;
                const row = Math.ceil(len / 4);
                const lastRow = Math.ceil((index + 1) / 4);
                return lastRow >= row;
            },

            startDrag() {
                // 没有name的情况下没有requirementInput
                if (this.$refs.requirementInput) {
                    const inputs = this.$refs.requirementInput;
                    const len = inputs.length;
                    for (let i = 0; i < len; i++) {
                        if (inputs[i].showSuggestions) {
                            inputs[i].handleClose();
                            break;
                        }
                    }
                }

                this.dragging = true;
                $('#medicine-hover-popover').remove();
            },

            showDeleteIcon(medicine) {
                return !this.disabledForm && (medicine.goodsId || medicine.name);
            },

            /**
             * @desc 药品禁用 || 库存不足 || 无库存信息
             * <AUTHOR> Yang
             * @date 2021-03-30 15:46:13
             */
            showWarnTips(chineseMedicine) {
                // 自备不校验库存
                if (chineseMedicine.chargeType === OutpatientChargeTypeEnum.NO_CHARGE) return false;

                // 不检查库存
                if (!this.needCheckStock) return false;

                // 禁用状态
                if (this.disabledForm) return false;
                const {
                    noStocks,
                    medicineCadn,
                    name,
                    unitCount,
                    stockPieceCount,
                } = chineseMedicine;
                const doseCount = this.form.doseCount || 1;

                // 填写name unitCount后才判断库存信息
                if ((medicineCadn || name)) {
                    const usedCount = Big(getSafeNumber(unitCount)).times(getSafeNumber(doseCount || 1), 1).toNumber();
                    const isShortage = usedCount > stockPieceCount;

                    // 虚拟药房不判断库存不足情况，但是需要判断 无商品资料 和 禁用
                    if (this.form.pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY) {
                        return isDisabledGoods(chineseMedicine).flag || noStocks;
                    }
                    return isDisabledGoods(chineseMedicine).flag || isShortage || noStocks;

                }
                return false;

            },

            warnTips(item) {
                const {
                    noStocks,
                    stockPieceCount,
                    unit,
                    eqCoefficient,
                } = item;
                if (noStocks) return '无库存';
                let isShortageTips = `库存不足(${stockPieceCount || 0}${unit || 'g'})`;
                if (eqCoefficient) {
                    const eqStockPieceCount = Number(((stockPieceCount || 0) * eqCoefficient).toFixed(2));
                    isShortageTips = `库存不足(${stockPieceCount || 0}${unit || 'g'}等效饮片${eqStockPieceCount}${unit || 'g'})`;
                }
                return isDisabledGoods(item).tips || isShortageTips;
            },

            editCurrentMedicine(index) {
                if (this.disabledForm) return false;
                this.currentIndex = index;
                this.$nextTick(() => {
                    const $tableTd = this.$el.querySelectorAll('.table-td')[index];
                    $tableTd && $tableTd.querySelector('.medicine-autocomplete input')?.focus();
                });
            },

            /** ----------------------------------------------------------------------
             *  删除中药处方的按钮
             */
            deletePres() {
                this.$emit('close', this.formIndex);
                this.$emit('queryVerify');
            },

            /**
             * @desc 显式删除中药
             * <AUTHOR>
             * @date 2019/05/29 11:33:28
             */
            deleteMedicine(index) {
                this.medicineList.splice(index, 1);
                this.$emit('queryVerify');
                this.outpatientFeeChange();
            },

            /** ----------------------------------------------------------------------
             * 中药每日剂量
             */
            chineseMedicineDailyDosage(queryString, cb) {
                let dailyDosageOptions = [];
                if (this.isAirPharmacy) {
                    const { businessScopeConfig } = this.vendorInfo;
                    const { dosageOptions } = businessScopeConfig || {};
                    dailyDosageOptions = dosageOptions;
                } else {
                    dailyDosageOptions = this.chineseMedicineConfig.dailyDosage;
                }
                // 调用 callback 返回建议列表的数据
                cb(dailyDosageOptions);
            },

            selectChineseMedicineSpecial(specialRequirement, index) {
                this.medicineList[index].specialRequirement = specialRequirement;
                this.$emit('queryVerify');
                this.outpatientFeeChange();
            },
            selectChineseMedicineUsage(usageArg = '') {
                if (!this.isAirPharmacy && this.form.usage === usageArg) return false;
                let usage = usageArg;
                const { businessScopeConfig } = this.vendorInfo;
                const {
                    usageOptions = [],
                    dosageOptions = [],
                    freqOptions = [],
                    usageLevelOptions = [],
                } = businessScopeConfig || {};

                if (this.isAirPharmacy) {
                    if (!usageOptions || usageOptions.length === 0) {
                        this.form.usage = '';
                        this.form.dailyDosage = '';
                        this.form.freq = '';
                        this.form.usageLevel = '';
                        this.form.usageDays = '';
                        return;
                    }
                    const { value } = usageOptions.find((x) => x.defaultValue) || {};
                    usage = usageOptions.map((x) => x.value).includes(usageArg) ?
                        usageArg :
                        value || usageOptions[0]?.value || '';
                }

                this.form.usage = usage;

                const {
                    dailyDosage,
                    freq,
                    usageLevel,
                    usageDays,
                } = this.chinesePRUsageDefault[usage] || {};

                // 空中药房不同的 制法会对应不同的服用量
                if (this.form.pharmacyType === PharmacyTypeEnum.AIR_PHARMACY) {
                    if (dosageOptions.length > 0) {
                        // 当前用法在配置中，保留用法不切换
                        const isCurrentInOptions = dosageOptions.map((x) => x.value).includes(this.form.dailyDosage);
                        if (!isCurrentInOptions) {
                            // 默认用法在配置中就用默认用法
                            // 默认用法不在就用供应商配置的默认用法
                            // 供应商没有配置默认就用第一个
                            const isDefaultInOptions = dosageOptions.map((x) => x.value).includes(dailyDosage);
                            const { value } = dosageOptions.find((x) => x.defaultValue) || {};
                            this.form.dailyDosage = isDefaultInOptions ?
                                dailyDosage :
                                (value || dosageOptions[0]?.value || '');
                        }
                    } else {
                        this.form.dailyDosage = '';
                    }
                    if (freqOptions.length > 0) {
                        const isCurrentInOptions = freqOptions.map((x) => x.value).includes(this.form.freq);
                        if (!isCurrentInOptions) {
                            const isDefaultInOptions = freqOptions.map((x) => x.value).includes(freq);
                            const { value } = freqOptions.find((x) => x.defaultValue) || {};
                            this.form.freq = isDefaultInOptions ?
                                freq :
                                (value || freqOptions[0]?.value || '');
                        }
                    } else {
                        this.form.freq = '';
                    }
                    if (usageLevelOptions.length > 0) {
                        const isCurrentInOptions = usageLevelOptions.map((x) => x.value).includes(this.form.usageLevel);
                        if (!isCurrentInOptions) {
                            const isDefaultInOptions = usageLevelOptions.map((x) => x.value).includes(usageLevel);
                            const { value } = usageLevelOptions.find((x) => x.defaultValue) || {};
                            this.form.usageLevel = isDefaultInOptions ?
                                usageLevel :
                                (value || usageLevelOptions[0]?.value || '');
                        }
                    } else {
                        this.form.usageLevel = '';
                    }
                } else {
                    this.form.dailyDosage = dailyDosage || '';
                    this.form.freq = freq || '';
                    this.form.usageLevel = usageLevel || '';
                    this.form.usageDays = usageDays || '';
                }

                if (this.isSpecialUsages) {
                    this.form.dailyDosage = '';
                } else {
                    this.form.usageDays = '';
                }
                this.$emit('queryVerify');
                this.calcUsageDays();
                this.outpatientFeeChange();
            },

            async changeMedicineState() {
                if (this.form.pharmacyType !== PharmacyTypeEnum.VIRTUAL_PHARMACY) return;
                await this.$nextTick();
                await this.calcBagsNumber();
                this.outpatientFeeChange();
                this.formCalcFee();
            },

            /**
             * @desc 计算复用天数，规则：总重量 * 成品率 / 服用量 * 频率
             * <AUTHOR>
             * @date 2021-04-26 11:01:12
             * @params
             * @return
             */
            calcUsageDays() {
                if (!this.isSpecialUsages) return;
                let value = 0;
                if (this.form.pharmacyType === PharmacyTypeEnum.AIR_PHARMACY) {
                    const {
                        usageScopeId,
                        medicineStateScopeId,
                    } = this.form;

                    // 制膏-袋装
                    // 制膏-瓶装
                    // 制丸-全部
                    // 打粉-全部
                    // 以上类型 才算服用天数
                    if (usageScopeId === UsageScopeIdEnum.zhiWan ||
                        usageScopeId === UsageScopeIdEnum.daFen ||
                        medicineStateScopeId === MedicineStateScopeIdEnum.pingZhuang ||
                        medicineStateScopeId === MedicineStateScopeIdEnum.daiZhuang
                    ) {
                        const {
                            doseCount,
                            usageLevel,
                            freq,
                            finishedRate,
                        } = this.form;

                        value = this.calcUsageDaysValue({
                            usageLevel,
                            freq,
                            doseCount,
                            finishedRate,
                        });


                    }
                } else {
                    const {
                        doseCount,
                        usageLevel,
                        freq,
                        usage,
                    } = this.form;
                    const finishedRateMap = {
                        '制膏': 0.4,
                        '制丸': 0.8,
                        '打粉': 0.8,
                    };
                    const finishedRate = finishedRateMap[usage];

                    value = this.calcUsageDaysValue({
                        usageLevel,
                        freq,
                        doseCount,
                        finishedRate,
                    });

                }

                if (value && !isNaN(value) && isFinite(value)) {
                    this.form.usageDays = `约服${Math.ceil(value)}天`;
                } else {
                    this.form.usageDays = '';
                }
            },

            calcUsageDaysValue({
                usageLevel,
                freq,
                doseCount,
                finishedRate,
            }) {
                const matchedUL = usageLevel.match(/^每次([0-9]+)g$/);
                const _usageLevelValue = matchedUL ? matchedUL[1] : 0;

                const matchedFreq = freq.match(/^1日([0-9]+)次$/);
                const _freqValue = matchedFreq ? matchedFreq[1] : 0;

                const value = this.doseTotalWeight *
                    (doseCount || 0) *
                    finishedRate / (_usageLevelValue * _freqValue);
                return value;
            },

            async selectChineseMedicineDailyDosage(freq) {
                this.dailyDosageError = false;
                this.form.dailyDosage = freq.name;
                await this.calcBagsNumber();
                this.outpatientFeeChange();
                this.formCalcFee();
                this.$emit('queryVerify');
            },

            async selectChineseMedicineFreq(freq) {
                this.freqError = false;
                this.form.freq = freq;
                this.calcUsageDays();
                await this.calcBagsNumber();
                this.outpatientFeeChange();
                this.formCalcFee();
                this.$emit('queryVerify');
            },

            async selectChineseMedicineUsageLevel(usageLevel) {
                this.form.usageLevel = usageLevel;
                this.calcUsageDays();
                await this.calcBagsNumber();
                this.outpatientFeeChange();
                this.formCalcFee();
                this.$emit('queryVerify');
            },
            selectChineseMedicineUsageDays() {
                this.outpatientFeeChange();
                this.$emit('queryVerify');
            },

            selectChineseMedicineRequirement(requirement) {
                this.form.requirement = requirement;
                this.outpatientFeeChange();
                this.$emit('queryVerify');
            },

            blurHandle(medicine, $autocomplete) {
                // this.currentIndex = -1;
                if (!medicine.name) {
                    if ($autocomplete &&
                        typeof $autocomplete.clearQueryString === 'function') {
                        $autocomplete.clearQueryString();
                    }
                }
            },

            handleClosePanel(medicine, index) {
                // 确保只关闭当前正在编辑的组件
                if (this.currentIndex === index) {
                    this.currentIndex = -1;
                    if (!medicine.name) {
                        // ref在循环里时，refs返回的是一个数组
                        const $autocomplete = this.$refs['chinese-autocomplete'][0];
                        if ($autocomplete && typeof $autocomplete.clearQueryString === 'function') {
                            $autocomplete.clearQueryString();
                        }
                    }
                }
            },
            handleDoubleClick(event) {
                if (!event.target.value) return;
                event.target.selectionStart = 0;
                event.target.selectionEnd = event.target.value.length;
            },
            async changeMedicine(newMedicine, mIndex) {
                if (this.supportEqPieces) {
                    const { productInfo } = newMedicine;
                    const {
                        typeId, pieceUnit, eqCoefficient,
                    } = productInfo || {};
                    if (
                        typeId === GoodsTypeIdEnum.MEDICINE_CHINESE_GRANULE &&
                        (pieceUnit === 'g' || pieceUnit === '克')
                    ) {
                        newMedicine.eqCoefficient = eqCoefficient;
                    }
                }
                const {
                    repeatType, repeatIndex,
                } = this.findRepeatIndex(newMedicine) || {};
                // 有重复的做toast提示
                if (repeatIndex > -1 && this.currentIndex !== repeatIndex) {
                    if (repeatType === RepeatTypeEnum.ALL) {
                        this.$Toast({
                            message: `处方中已添加${newMedicine.name}`,
                            type: 'error',
                            duration: 1500,
                            referenceEl: this.$el.querySelectorAll('.table-td')[this.currentIndex].querySelector('.name-wrapper'),
                        });
                        return false;
                    }
                    if (this.$abcSocialSecurity.config.isNeimenggu && repeatType === RepeatTypeEnum.ONLY_NAME) {
                        this.$Toast({
                            message: `存在同名药品：${newMedicine.name}`,
                            type: 'error',
                            duration: 1500,
                            referenceEl: this.$el.querySelectorAll('.table-td')[this.currentIndex].querySelector('.name-wrapper'),
                        });
                    }
                }

                const index = this.currentIndex;
                if (index === -1) return;
                const oldMedicine = this.medicineList[index];
                // 当老的药品和新换的药品 goodsId name 一样则不更新keyId
                if (oldMedicine.goodsId === newMedicine.goodsId && oldMedicine.name === newMedicine.name) {
                    newMedicine.keyId = oldMedicine.keyId || newMedicine.keyId;
                }
                this.medicineList.splice(index, 1, newMedicine);
                this.currentIndex = -1;
                this.$nextTick(() => {
                    this._timer = setTimeout(() => {
                        this.$el
                            .querySelectorAll('.table-td')[index]
                            .querySelector('.count-wrapper input')
                            .focus();
                    }, 1);
                });
                this.insertAfterCM();
                if (newMedicine.medicineCadn) {
                    // 自动填写 煎法
                    const { data } = await CDSSAPI.fetchSpecialRequirementRememberSetting({
                        goodsId: newMedicine.goodsId,
                        cadn: newMedicine.medicineCadn,
                    });
                    if (data && data.isRemember) {
                        newMedicine.rememberSpecialRequirement = data.isRemember;
                        newMedicine.specialRequirement = data.specialRequirement;
                        const el = this.$refs.requirementInput[mIndex].$el;
                        this.handleRemarkInput(newMedicine, el);
                    }
                }
                this.$emit('queryVerify');
                this.$emit('add-prescription-form-item', newMedicine);

                this.outpatientFeeChange();
            },

            /**
             * @desc 在后面新增一个中药item
             * <AUTHOR>
             * @date 2020/04/23 11:31:58
             */
            insertAfterCM() {
                if (this.disabledForm || this.disabledAdd) return;
                if (!this.medicineList.find((item) => !item.goodsId && !item.name)) {
                    this.medicineList.push({
                        keyId: createGUID(),
                        unit: 'g',
                        goodsId: null,
                        medicineCadn: '',
                        name: '',
                        specialRequirement: '',
                        unitCount: '',
                    });
                }
            },
            /** ----------------------------------------------------------------------
             *  比较是否有重复项目
             */
            compareRepeat(item) {
                if (this.disabledForm) return false;
                let count = 0;
                this.medicineList.forEach((m) => {
                    if (m.name &&
                        m.name === item.name &&
                        (m.goodsId || '') === (item.goodsId || '')) {
                        count++;
                    }
                });
                return count >= 2;
            },

            /**
             * @desc 找到已有药品的index
             * <AUTHOR>
             * @date 2018/07/10 15:45:07
             */
            findRepeatIndex(item) {
                const sameMedicineIndex = this.medicineList.findIndex((m) => {
                    return m.name &&
                        m.name === item.name &&
                        (m.goodsId || '') === (item.goodsId || '');
                });
                if (sameMedicineIndex > -1) {
                    return {
                        repeatType: RepeatTypeEnum.ALL,
                        repeatIndex: sameMedicineIndex,
                    };
                }

                const sameNameIndex = this.medicineList.findIndex((m) => {
                    return m.name && m.name === item.name;
                });
                if (sameNameIndex > -1) {
                    return {
                        repeatType: RepeatTypeEnum.ONLY_NAME,
                        repeatIndex: sameNameIndex,
                    };
                }
                return null;
            },

            /**
             * @desc 支持回车进入下一个
             * <AUTHOR>
             * @date 2018/07/06 16:57:21
             */
            enterEvent(e) {
                // 快速回到药品搜索input
                if (this._fromAutocomple) {
                    this.$nextTick(() => {
                        this.$el.querySelector('.medicine-autocomplete input').focus();
                        this._fromAutocomple = false;
                    });
                    return false;
                }

                // 找到所有的非disabled的input输入框
                const inputs = $(this.$el).find('.abc-input__inner').not(':disabled');

                const targetIndex = inputs.index(e.target);

                let nextInput = inputs[targetIndex + 1];
                if (!nextInput) {
                    this.$el.querySelector('.medicine-autocomplete input').focus();
                    return false;
                }

                if (nextInput.tabIndex === -1) {
                    nextInput = inputs[targetIndex + 2];
                }
                nextInput && this.$nextTick(() => {
                    nextInput.select();
                    nextInput.focus();
                });
            },

            async clickDecoction() {
                if (this.disabledForm) return false;
                if (this.form.isDecoction) {
                    this.form.isDecoction = false;
                    this.form.processRemark = '';
                    this.form.usageType = '';
                    this.form.usageSubType = '';
                    this.form.processBagUnitCount = '';
                    this.form.totalProcessCount = '';
                    this.form.processInfo = null;
                    this.form.ingredientInfo = null;
                    this.changePharmacyHandler();
                    this.outpatientFeeChange();
                } else {
                    await this.calcBagsNumber(true);
                    this.showProcessDialog = true;
                }
            },

            // 保存模板
            saveCommon() {
                if (this.medicineList.filter((item) => item.name).length === 0) {
                    this.$alert({
                        type: 'warn',
                        title: '提示',
                        content: '处方药品为空，不能保存为模板',
                    });
                    return false;
                }
                this.dialogVisible = true;
            },

            changePharmacyHandler() {
                const defaultPharmacy = this.form.specification === '中药颗粒' ? this.granuleDefaultPharmacy : this.piecesDefaultPharmacy;
                // 切换加工方式后，药房发生变更需要重置药品
                if (this.form.pharmacyNo !== defaultPharmacy.no) {
                    Object.assign(this.form, {
                        pharmacyType: defaultPharmacy.type,
                        pharmacyNo: defaultPharmacy.no,
                        pharmacyName: defaultPharmacy.name,
                    });
                    this.change2Local();
                }
            },

            /**
             * @desc 确认本地加工信息
             * <AUTHOR>
             * @date 2022-06-17 16:17:07
             */
            async confirmProcessHandler(usageInfo) {
                const {
                    usageType,
                    usageSubType,
                    processRemark,
                    processBagUnitCount,
                    totalProcessCount,
                    processInfo,
                    ingredientInfo,
                } = usageInfo;
                this.$set(this.form, 'isDecoction', true);
                this.$set(this.form, 'processInfo', processInfo);
                this.$set(this.form, 'ingredientInfo', ingredientInfo);
                this.form.usageType = usageType;
                this.form.usageSubType = usageSubType;
                this.form.processRemark = processRemark;
                this.form.processBagUnitCount = processBagUnitCount;
                this.form.totalProcessCount = totalProcessCount;
                this.changePharmacyHandler();
                this.outpatientFeeChange();
            },
            confirmAirProcessHandler({
                processBagUnitCount,
                totalProcessCount,
                processRemark,
            }) {
                this.form.processBagUnitCount = processBagUnitCount;
                this.form.totalProcessCount = totalProcessCount;
                this.form.processRemark = processRemark;
                this.outpatientFeeChange();
            },

            /**
             * @desc 切换药房类型为本地药房, 此时需要更新为本店的药品info， 逻辑同处方模版
             * <AUTHOR> Yang
             * @date 2020-07-08 19:17:08
             */
            async change2Local(specification) {
                if (this.form.pharmacyType !== PharmacyTypeEnum.LOCAL_PHARMACY) return;
                this.initEqConversionRule(specification);
                const cMSpec = specification || this.form.specification;

                this.handleChangePharmacyLoading(true);
                await renewChineseMedicines(this.form, {
                    cMSpec,
                    pharmacyNo: this.form.pharmacyNo,
                    supportEqPieces: this.supportEqPieces,
                });
                this.handleChangePharmacyLoading(false);
                // 修改药房后需要清空议价
                const hasBargain = clearFormBargainHandler(this.form);
                if (hasBargain) {
                    this.$Toast({
                        message: '价格变动导致议价清空，请重新确认价格',
                        type: 'error',
                        duration: 2000,
                        referenceEl: this.$refs['vendor-popover'].$el,
                    });
                }
                this.vendorInfo = {
                    vendorUsageScopeId: '',
                };
                this.outpatientFeeChange();
            },
            handleChangePharmacyLoading(val) {
                if (!this.outpatientEditForm) return;
                this.outpatientEditForm.finishLoading = val;
            },

            async changeVendor(vendor) {
                if (this.disabledForm) return;
                const oldVendor = clone(this.vendorInfo);
                this.$set(this.form, 'finishedRateMin', +vendor.finishedRateMin);
                this.$set(this.form, 'finishedRate', +vendor.finishedRate);
                this.form.totalPrice = vendor.totalPrice;
                this.minimum = +vendor.minimum;
                this.vendorAvailableMedicalStates = vendor.vendorAvailableMedicalStates;
                this.vendorInfo = vendor;
                this.calcUsageDays();
                if (this.isAirPharmacy && oldVendor.vendorUsageScopeId !== vendor.vendorUsageScopeId) {
                    // 其它药房类型切过来不初始化用法
                    this.selectChineseMedicineUsage(this.form.usage);
                    // 切换药房晚于切换用法的执行时机,所以切换用法时拿到的药房数据还是旧的
                    // 需要在切换药房完成后再次计算
                    await this.calcBagsNumber();
                }
                if (this.showProcessInfo) {
                    // 需要在切换药房完成后再次计算
                    await this.calcBagsNumber();
                }
                this.outpatientFeeChange();
            },

            /**
             * @desc 点击用户列表，供应商列表，会默认切换药房，此时需要清空议价
             * @desc 避免vendor一些初始化动作会触发changeVendor，需要用户明确点击
             * <AUTHOR>
             * @date 2022-03-24 16:33:19
             */
            handleUserChangeVendor() {
                // 修改药房后需要清空议价
                const hasBargain = clearFormBargainHandler(this.form);
                if (hasBargain) {
                    this.$Toast({
                        message: '价格变动导致议价清空，请重新确认价格',
                        type: 'error',
                        duration: 2000,
                        referenceEl: this.$refs['vendor-popover'].$el,
                    });
                }
                // 修改药房后需要清空当量
                if (this.enableEqCoefficient) {
                    this.medicineList.forEach((item) => {
                        this.$set(item, 'unitCount', item.eqUnitCount || item.unitCount);
                        this.$set(item, 'eqUnitCount', '');
                        delete item.eqCoefficient;
                    });
                }
            },
            handleRemarkInput(item, el) {
                if (item.specialRequirement && item.specialRequirement.indexOf('【自备】') > -1) {
                    this.$set(item, 'chargeType', OutpatientChargeTypeEnum.NO_CHARGE);
                    this.$Toast({
                        message: '备注为【自备】，该药品将不会纳入划价收费',
                        duration: 1500,
                        referenceEl: el,
                    });
                } else {
                    this.$set(item, 'chargeType', OutpatientChargeTypeEnum.DEFAULT);
                }
            },
            handleNameLeft(index) {
                index -= 1;
                if (index < 0) return;
                this.moveCountPointer(index);
            },
            handleNameRight(index) {
                this.moveCountPointer(index);
            },
            handleNameUp(index) {
                index -= 4;
                if (index < 0) return;
                this.moveNamePointer(index);
            },
            handleNameDown(index) {
                index += 4;
                if (index > this.medicineList.length - 1) {
                    return;
                }
                this.moveNamePointer(index);
            },

            handleCountLeft(index) {
                this.moveNamePointer(index);
            },
            handleCountRight(index) {
                index += 1;
                if (index > this.medicineList.length - 1) return;
                this.moveNamePointer(index);
            },
            handleCountUp(index) {
                index -= 4;
                if (index < 0) return;
                this.moveRemarkPointer(index);
            },
            handleCountDown(index) {
                this.moveRemarkPointer(index);
            },

            handleRemarkLeft(index) {
                index -= 1;
                if (index < 0) return;
                this.moveRemarkPointer(index);
            },
            handleRemarkRight(index) {
                index += 1;
                if (index > this.medicineList.length - 1) return;
                this.moveRemarkPointer(index);
            },
            handleRemarkUp(index) {
                this.moveCountPointer(index);
            },
            handleRemarkDown(index) {
                index += 4;
                if (index > this.medicineList.length - 1) {
                    this.changeInputPointer('dose-count');
                    return;
                }
                this.moveCountPointer(index);
            },

            changeInputPointer(className) {
                const $input = this.$el.querySelector(`.${className}`);
                if ($input) {
                    const nextInput = $input.querySelector('input');
                    this.focusSelectHandle(nextInput);
                }
            },


            moveNamePointer(index) {
                this.currentIndex = index;
                this.$nextTick(() => {
                    const $tableTd = this.$el.querySelectorAll('.table-td');
                    const nextInput = $tableTd[index].querySelector('.name-wrapper input');
                    this.focusSelectHandle(nextInput);
                });
            },
            moveCountPointer(index) {
                const $tableTd = this.$el.querySelectorAll('.table-td');
                const nextInput = $tableTd[index].querySelector('.count-wrapper input');
                this.focusSelectHandle(nextInput);
            },
            moveRemarkPointer(index) {
                this.currentCountIndex = index;
                this.$nextTick(() => {
                    const $tableTd = this.$el.querySelectorAll('.table-td');
                    const nextInput = $tableTd[index].querySelector('.chinese-special-requirement input');
                    this.focusSelectHandle(nextInput);
                });
            },

            focusSelectHandle($input) {
                this._timer && clearTimeout(this._timer);
                if (!$input) return;
                $input.selectionStart = 0;
                $input.selectionEnd = $input.value ? $input.value.length : 0;
                this._timer = setTimeout(() => {
                    $input.focus && $input.focus();
                    $input.select && $input.select();
                }, 0);
            },

            /**
             * @desc 门诊单内容变更
             * <AUTHOR>
             * @date 2022/06/06 10:47:44
             */
            outpatientFeeChange() {
                this.$abcEventBus.$emit('outpatient-fee-change', {
                    from: '中药处方',
                    needCalcFee: true,
                });
            },

            async handleClickProcessInfo() {
                if (this.disabledForm) return false;
                const {
                    pharmacyType,
                    processBagUnitCount,
                    totalProcessCount,
                } = this.form;

                if (pharmacyType === PharmacyTypeEnum.LOCAL_PHARMACY) {
                    this.showProcessDialog = true;
                    return;
                }

                if (
                    pharmacyType === PharmacyTypeEnum.AIR_PHARMACY ||
                    pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY
                ) {
                    if (!processBagUnitCount && !totalProcessCount) {
                        await this.calcBagsNumber();
                        this.outpatientFeeChange();
                    }
                    this.showAirProcessDialog = true;
                }
            },

            /**
             * @desc 空中药房走门诊算费，本地药房自己算
             */
            async formCalcFee() {
                if (!this.form.isDecoction) return;
                const { pharmacyType } = this.form;
                if (pharmacyType !== PharmacyTypeEnum.LOCAL_PHARMACY) return;
                const calcData = ChargeAPI.getLocalProcessPostData(this.form);
                const res = await ChargeAPI.localPharmacyCalcProcessFee({
                    forms: [calcData],
                });
                if (!res) return;
                const { data } = res;
                const { forms } = data || {};
                if (!forms) return;
                const form = forms[0];
                if (form && (form.keyId === this.form.id || form.keyId === this.form.keyId)) {
                    this.$set(this.form, 'ingredientPrice', form.ingredientPrice);
                    this.$set(this.form, 'processPrice', form.processPrice);
                    this.form.processInfo = this.form.processInfo || {};
                    Object.assign(this.form.processInfo, {
                        displayTotalPrice: form.processPrice,
                    });
                    this.form.ingredientInfo = this.form.ingredientInfo || {};
                    Object.assign(this.form.ingredientInfo, {
                        displayTotalPrice: form.ingredientPrice,
                    });
                }
            },

            /**
             * @desc 计算一剂煎几袋、总袋数
             * @desc 用法用量：x日y剂，1日z次
             * @desc 每剂煎药袋数 =(z * x) / y;
             * <AUTHOR>
             * @date 2022/08/23 15:37:14
             */
            async calcBagsNumber(forceCall = false) {
                try {
                    const {
                        freq,
                        dailyDosage,
                        doseCount,
                        specification,
                        pharmacyType,
                        medicineStateScopeId,
                        usageLevel,
                    } = this.form;
                    if (this.form.pharmacyType === PharmacyTypeEnum.AIR_PHARMACY) {
                        this.form.isDecoction = (
                            this.isYPDaiJian || this.form.medicineStateScopeId === MedicineStateScopeIdEnum.keLi ||
                            this.form.usageScopeId === UsageScopeIdEnum.zhiGao ||
                            this.form.usageScopeId === UsageScopeIdEnum.daFen ||
                            this.form.usageScopeId === UsageScopeIdEnum.zhiWan
                        );
                    }
                    if (!forceCall && !this.form.isDecoction) {
                        this.$set(this.form, 'processBagUnitCount', '');
                        this.$set(this.form, 'totalProcessCount', '');
                        return;
                    }
                    if (
                        pharmacyType === PharmacyTypeEnum.AIR_PHARMACY &&
                        medicineStateScopeId !== MedicineStateScopeIdEnum.keLi &&
                        medicineStateScopeId !== MedicineStateScopeIdEnum.daiJian
                    ) {
                        this.$set(this.form, 'processBagUnitCount', '');
                        this.$set(this.form, 'totalProcessCount', '');
                        return;
                    }
                    if (!freq || !dailyDosage) {
                        this.$set(this.form, 'processBagUnitCount', '');
                        this.$set(this.form, 'totalProcessCount', '');
                        return;
                    }
                    if (pharmacyType === PharmacyTypeEnum.AIR_PHARMACY &&
                        medicineStateScopeId === MedicineStateScopeIdEnum.keLi &&
                        this.quantityConfiguration.doseCountOption
                    ) {
                        this.$set(this.form, 'processBagUnitCount', this.quantityConfiguration.bagUnitCount);
                        this.$set(this.form, 'totalProcessCount', '');
                        return;
                    }
                    const {
                        bagUnitCount,
                        bagTotalCount,
                    } = await ChargeAPI.calculateBagCount({
                        doseCount,
                        dailyDosage,
                        freq,
                        pharmacyType,
                        usageLevel,
                        type: specification === '中药颗粒' ? 2 : 1,
                    });
                    this.$set(this.form, 'processBagUnitCount', bagUnitCount);
                    this.$set(this.form, 'totalProcessCount', bagTotalCount);
                } catch (e) {
                    console.error(e);
                }
            },

            showDelivery() {
                if (this.disabledForm) return;
                if (this.form.pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY) {
                    this.showDeliverVirtualDialog = true;
                    return;
                }
                this.showDeliverDialog = true;
            },

            async deliverConfirmHandler(data) {
                const deliveryInfo = this.form.deliveryInfo || {};
                const newDeliveryInfo = Object.assign({}, deliveryInfo, data);
                newDeliveryInfo.deliveryCompanyId = data.deliveryCompany?.id;
                this.$set(this.form, 'deliveryInfo', newDeliveryInfo);
                this.showDeliverDialog = false;
                this.showDeliverVirtualDialog = false;
                this.outpatientFeeChange();
            },

            async fetchDeliveryTrace() {
                const {
                    id,
                    deliveryInfo,
                } = this.form;
                const { deliveryOrderNo } = deliveryInfo || {};
                if (!id || !deliveryOrderNo) return;
                const { data } = await OutpatientAPI.getDeliveryTraceList(id);
                const { traceList } = data || {};
                this.$set(this.form.deliveryInfo, 'traceList', traceList);
            },


            searchDetailImage({
                title, link,
            }) {
                new DetailImageDialog({
                    title: `${title}`,
                    imgUrl: link,
                    writeAirPrescriptionFunc: this.handleToAirPrescription,
                    freeOpenSuccessFunc: async () => {
                        const h = this.$createElement;
                        this.applySuccessModal(h, {
                            updateProfitRateFunc: this.handleModifyProfitRate,
                            toAirPrescriptionFunc: this.handleToAirPrescription,
                        });
                    },
                }).generateDialog({
                    parent: this,
                });
            },
            handleModifyProfitRate() {
                this.$router.push({
                    name: 'airpharmacyservice',
                });
            },
            async handleToAirPrescription() {
                this.$refs['vendor-popover'].showPopover = true;
                await this.$nextTick();
                this.$refs['vendor-popover'].changePharmacyType(PharmacyTypeEnum.AIR_PHARMACY);
            },
            /**
             * 点击空中药房推广语
             */
            handleAdvertisement() {
                const {
                    linkType, linkOpenType, link, id, title,
                } = this.airPharmacyPromotion;
                if (!link) return;
                // 统计点击广告的行为
                MessageAPI.updatePushClick(id);
                if (linkType === 2) {
                    // 图片查看从abc-preview改为abc-popover
                    this.searchDetailImage({
                        title, link,
                    });
                } else if (linkOpenType === 1) {
                    // 当前页面跳转
                    window.location.href = link;
                } else {
                    // 新标签页跳转
                    windowOpen(link, '', '', PROTOCOL.HTTPS);
                }
            },
            setIsDecoction() {
                if (this.form.pharmacyType === PharmacyTypeEnum.VIRTUAL_PHARMACY) {
                    this.form.isDecoction = this.isYPDaiJian;
                } else if (this.form.pharmacyType === PharmacyTypeEnum.AIR_PHARMACY) {
                    this.form.isDecoction = (this.isYPDaiJian || this.form.medicineStateScopeId === MedicineStateScopeIdEnum.keLi);
                } else {
                    this.form.isDecoction = false;
                }
            },

            calcDeliveryFee() {
                this._calcVirtualPharmacyDeliveryFee();
            },

            async calcVirtualPharmacyDeliveryFee() {
                if (this.disabledForm) return false;
                if (this.form.pharmacyType !== PharmacyTypeEnum.VIRTUAL_PHARMACY) return;

                const postData = {
                    chargeForms: [trans2ChargeForm(this.form)],
                };

                try {
                    const { data } = await ChargeAPI.virtualPharmacyCalcDelivery(postData);
                    const result = data.rows[0];
                    if (!result) return;
                    if (this.form.deliveryInfo) {
                        this.form.deliveryInfo.deliveryPrice = result.expressDeliveryFee;
                    }
                } catch (e) {
                    console.log(e);
                }
            },

            quickSelect(goodsList) {
                goodsList.forEach(async (goods) => {
                    const prescriptionItem = getPrescriptionItemStruct(goods);
                    prescriptionItem.cMSpec = goods.CMSpec;
                    this.currentIndex = this.form.prescriptionFormItems.length - 1;
                    this.changeMedicine(prescriptionItem, this.currentIndex);
                    // 是诊所药的话会去换一次medicineid
                    if (prescriptionItem.goodsId) {
                        if (!this.isOpenSource) return false;
                        const { data } = await GoodsApi.fetchGoods(prescriptionItem.goodsId);
                        Object.assign(prescriptionItem, {
                            unitPrice: data.piecePrice || 0,
                        });
                    }
                });
                this.currentIndex = -1;
            },

            handleCopyPR(data) {
                const { from } = data || {};
                if (from === '复制处方') {
                    this.formCalcFee();
                }
            },

            getUnitCountField(medicine) {
                if (this.supportEqPieces && medicine.eqCoefficient) {
                    return 'eqUnitCount';
                }
                return 'unitCount';
            },

            changeEqConversionRule(val, index, oldValue) {
                if (oldValue === val) return;
                this.medicineList.forEach((item) => {
                    const { productInfo } = item;
                    const { eqCoefficient } = productInfo || {};
                    if (val === EqConversionRuleEnum.EQ_PIECES) {
                        this.$set(item, 'eqCoefficient', eqCoefficient);
                        this.$set(item, 'eqUnitCount', item.unitCount);
                        this.$set(item, 'unitCount', '');
                    } else if (val === EqConversionRuleEnum.REAL_GRANULE) {
                        this.$set(item, 'unitCount', item.eqUnitCount || item.unitCount);
                        this.$set(item, 'eqUnitCount', '');
                        delete item.eqCoefficient;
                    }
                });
                this.outpatientFeeChange();
            },
        },
    };
</script>

<style module lang="scss" src="@/styles/theme.module.scss">
</style>
